/*
 * This file was generated by the Gradle 'init' task.
 *
 * This project uses @Incubating APIs which are subject to change.
 */

plugins {
    kotlin("plugin.serialization") version "2.0.20"
}

dependencies {
    api(project(":ecco-buildings"))
    api(project(":ecco-calendar"))
    implementation(project(":ecco-calendar-cosmo")) // Just for is
    implementation(project(":ecco-dao")) // For PredicateSupport - should look to move into a reports repository

    implementation(project(":ecco-evidence"))
    api(project(":ecco-group-support"))
    api(project(":ecco-hr"))
    api(project(":ecco-incidents"))
    api(project(":ecco-repairs"))
    api(project(":ecco-messaging"))
    api(project(":ecco-notifications"))
    api(project(":ecco-managedvoids"))
    implementation(project(":ecco-infrastructure"))
    api(project(":ecco-rota"))
    api(project(":ecco-contracts"))
    api(project(":ecco-finance"))
    api(project(":ecco-security-core"))
    api(project(":ecco-service"))
    api(project(":ecco-service-config"))
    implementation(project(":ecco-workflow"))
    api(project(":ecco-reports"))
    implementation(project(":ecco-workflow-activiti"))

    testImplementation(project(":ecco-buildings"))
    testImplementation(project(":test-support"))

    implementation("com.aayushatharva.brotli4j:brotli4j:1.7.1")
    implementation("org.apache.tomcat:tomcat-jdbc")
    implementation("com.github.fge:json-patch:1.9")
    implementation("com.google.zxing:core:3.5.3")
    implementation("com.google.zxing:javase:3.5.3")
    implementation("com.jayway.jsonpath:json-path")
    implementation("org.springframework.boot:spring-boot-autoconfigure")
    implementation("org.springframework.hateoas:spring-hateoas")
    implementation("org.springframework.security:spring-security-config")
    implementation("org.springframework.security:spring-security-web")
    implementation("org.springframework:spring-context-support")
    api("org.springframework:spring-webmvc")
    implementation("org.springframework:spring-websocket")
    implementation("org.synyx:messagesource")
    implementation("com.google.guava:guava")
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-core:1.6.2")
}

description = "ecco-web-api"
