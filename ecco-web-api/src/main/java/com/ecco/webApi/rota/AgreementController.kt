package com.ecco.webApi.rota

import com.ecco.dao.DemandScheduleDirectTaskRepository
import com.ecco.dao.DemandScheduleRepository
import com.ecco.dao.ServiceAgreementRepository
import com.ecco.dom.agreements.DemandScheduleDirectTask
import com.ecco.dom.agreements.ServiceAgreement
import com.ecco.rota.service.RotaDelegator
import com.ecco.rota.service.RotaService
import com.ecco.rota.webApi.dto.AgreementResource
import com.ecco.rota.webApi.dto.DemandScheduleDirectTaskHandleDto
import com.ecco.rota.webApi.dto.DemandScheduleDto
import com.ecco.webApi.controllers.BaseWebApiController
import com.ecco.webApi.controllers.NotFoundException
import com.ecco.webApi.finance.RateCardToViewModel
import com.ecco.webApi.finance.RateCardViewModel
import com.ecco.webApi.taskFlow.ServiceRecipientTaskParams
import com.ecco.webApi.viewModels.Result
import org.joda.time.format.ISODateTimeFormat
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.composed.web.rest.json.GetJson
import org.springframework.composed.web.rest.json.PostJson
import org.springframework.security.core.Authentication
import org.springframework.web.bind.annotation.*
import java.io.IOException
import java.util.function.Function
import java.util.regex.Pattern
import java.util.stream.Collectors

/**
 * Web API for managing agreement and appointment schedules.
 * Initially this will be used for creating ad-hoc appointment schedules against a pre-existing agreement.
 * This API is rather more RESTful than [RotaController].
 *
 * URL patterns:
 *
 *  * GET  /rota/{demandedResourceFilter}/agreements/?date={relevantDate}&serviceRecipientFilter={..} - findAllAgreementsByDate
 *  * GET  /rota/agreements/serviceRecipient/{serviceRecipientId}/
 *  * POST /rota/agreements/serviceRecipient/{serviceRecipientId}/ - create new agreement
 *  * GET  /rota/agreements/{agreementId}/appointments/
 *  * GET  /rota/agreements/{agreementId}/resources/
 *  * GET  /rota/all/demands/1{demandScheduleId}
 *
 *
 */
@RestController
@RequestMapping("/rota")
class AgreementController(
    private val serviceAgreementRepository: ServiceAgreementRepository,
    private val demandScheduleRepository: DemandScheduleRepository,
    private val demandScheduleDirectTaskRepository: DemandScheduleDirectTaskRepository,
    private val agreementCommandHandler: ServiceAgreementHandler,
    private val resourceScheduleCommandHandler: ServiceRecipientResourceScheduleCommandHandler,
    private val appointmentScheduleCommandHandler: ServiceRecipientAppointmentScheduleCommandHandler,
    private val agreementResourceAssembler: AgreementResourceAssembler,
    @param:Qualifier("agreementResourceAssemblerNoSchedules")
    private val agreementResourceAssemblerNoSchedules: AgreementResourceAssembler,
    private val rotaService: RotaService,
    private val rotaDelegator: RotaDelegator,
) : BaseWebApiController() {
    private val demandResourceAssembler = DemandResourceAssembler(demandScheduleRepository)
    private val rateCardToViewModel = RateCardToViewModel()

    @GetJson("/{resourceFilter}/agreements/srIds/") // This one 1-3 sec on a busy rota and FIXME: we only use it to find relevant srIds
    fun findAllAgreementSrIdsByDemandAndScheduleDate(
        @PathVariable resourceFilter: String,
        @RequestParam(required = false) demandFilter: String?,
        @RequestParam("startDate") isoStartDateString: String,
        @RequestParam(value = "endDate", required = false) isoEndDateString: String?,
    ): List<Int> {
        val start = ISODateTimeFormat.date().parseDateTime(isoStartDateString).toLocalDate()
        val end = if (isoEndDateString == null) {
            start
        } else {
            ISODateTimeFormat.date().parseDateTime(isoEndDateString)
                .toLocalDate()
        }
        val handler = rotaDelegator.selectHandler(resourceFilter, demandFilter)
        val entities = rotaService.findAllAgreementSrIdsByDemandAndScheduleDate(
            handler,
            resourceFilter,
            demandFilter,
            start,
            end,
        )
        return entities.distinct()
        // return entities.stream().map { entity: ServiceAgreement -> entity.id.toInt() }.toList().distinct();
    }

    @GetJson("/{resourceFilter}/agreements/") // This one 1-3 sec on a busy rota and FIXME: we only use it to find relevant srIds
    fun findAllAgreementsByDemandAndScheduleDate(
        @PathVariable resourceFilter: String,
        @RequestParam(required = false) demandFilter: String?,
        @RequestParam("startDate") isoStartDateString: String,
        @RequestParam(value = "endDate", required = false) isoEndDateString: String?,
    ): List<AgreementResource> {
        val start = ISODateTimeFormat.date().parseDateTime(isoStartDateString).toLocalDate()
        val end = if (isoEndDateString == null) {
            start
        } else {
            ISODateTimeFormat.date().parseDateTime(isoEndDateString)
                .toLocalDate()
        }
        val handler = rotaDelegator.selectHandler(resourceFilter, demandFilter)
        val entities = rotaService.findAllAgreementsByDemandAndScheduleDate(
            handler,
            resourceFilter,
            demandFilter,
            start,
            end,
        )
        // the time spent is looping all the agreements
        return entities.stream().map { entity: ServiceAgreement? ->
            agreementResourceAssemblerNoSchedules.toModel(
                entity!!,
            )
        }
            .distinct() // TODO: This shouldn't be needed ... should be distinct before this point
            .collect(Collectors.toList())
    }

    /**
     * Handle an agreement.
     * TODO Could/should form part of ReferralTaskController.
     */
    @PostJson("/agreements/serviceRecipient/{serviceRecipientId}/")
    @Throws(IOException::class)
    fun handleAgreementChangeCommand(
        authentication: Authentication,
        @PathVariable serviceRecipientId: Int,
        @RequestBody requestBody: String,
    ): Result {
        val params = ServiceRecipientTaskParams(serviceRecipientId, ServiceAgreementCommandDto.TASK_SERVICEAGREEMENTS)
        return agreementCommandHandler.handleCommand(authentication, params, requestBody)
    }

    @GetJson("/agreements/serviceRecipient/{serviceRecipientId}/")
    fun listAgreementsByServiceRecipient(@PathVariable serviceRecipientId: Int): List<AgreementResource> {
        val entities = serviceAgreementRepository
            .findAllByServiceRecipientId(serviceRecipientId)
        return entities.stream().map { entity: ServiceAgreement ->
            agreementResourceAssembler.toModel(entity)
        }.collect(Collectors.toList())
    }

    @GetJson("/agreements/{agreementId}/")
    fun findAgreementById(@PathVariable agreementId: Long): AgreementResource {
        val agreement = serviceAgreementRepository.findById(agreementId).orElse(null)
        throwNotFoundIfNull(agreement, agreementId)
        return agreementResourceAssembler.toModel(agreement)
    }

    // TODO UNUSED - although a heatos link is provided for agreement 'id' comparisons (see usages of listAppointments)
    // TODO is actually used in the AgreementActor directly using restTemplate.getForEntity(appointmentScheduleUri...
    @GetJson("/agreements/{agreementId}/appointments/")
    fun listAppointments(@PathVariable agreementId: Long): List<DemandScheduleDto> {
        val agreement = serviceAgreementRepository.findById(agreementId).orElse(null)
        throwNotFoundIfNull(agreement, agreementId)
        return agreement.appointmentSchedules.stream().map(demandResourceAssembler).toList()
    }

    @GetJson("/agreements/{agreementId}/resources/")
    fun listResources(@PathVariable agreementId: Long): List<DemandScheduleDto> {
        val agreement = serviceAgreementRepository.findById(agreementId).orElse(null)
        throwNotFoundIfNull(agreement, agreementId)
        return agreement.resourceSchedules.stream().map(demandResourceAssembler).toList()
    }

    @GetJson("/agreements/{agreementId}/rateCards/")
    fun findRateCardsByAgreementId(@PathVariable agreementId: Long): List<RateCardViewModel> {
        val agreement = serviceAgreementRepository.findOne(agreementId) ?: throw NotFoundException(agreementId)
        return agreement.contract?.rateCards?.stream()
            ?.map(rateCardToViewModel)
            ?.collect(Collectors.toList())
            ?: emptyList()
    }

    /** Get one schedule  */
    @GetJson("/agreements/schedules/{scheduleId}")
    fun getAppointmentScheduleById(@PathVariable scheduleId: Long): DemandScheduleDto {
        val schedule = demandScheduleRepository.findOne(scheduleId)
        return demandResourceAssembler.toModel(schedule)
    }

    /** Get one schedule by calendar ref  */
    @GetJson("/agreements/schedules/ref/{scheduleRef}")
    fun getAppointmentScheduleByRef(@PathVariable scheduleRef: String?): DemandScheduleDto {
        // From DemandSchedule, see @PrePersist, the scheduleRef is a reference to the recurring entry, the entryHandleAsString (which is a NoteItem.uid)
        val schedule = demandScheduleRepository.findOneByEntryHandleAsString(scheduleRef).orElseThrow()
        return demandResourceAssembler.toModel(schedule)
    }

    /**
     * Create or edit an appointment for a service recipient [BaseServiceRecipient]
     * Previously as hateos on /agreements/{agreementId}/appointments/, but now matching resourceSchedule commands
     */
    @PostJson("/appointment-demand/{serviceRecipientId}")
    @Throws(IOException::class)
    fun appointmentSchedule(
        @PathVariable serviceRecipientId: Int,
        authentication: Authentication,
        @RequestBody requestBody: String,
    ): Result {
        val params = ServiceRecipientTaskParams(serviceRecipientId, ServiceAgreementCommandDto.TASK_SERVICEAGREEMENTS)
        return appointmentScheduleCommandHandler.handleCommand(authentication, params, requestBody)
    }

    /**
     * Create or edit a resource demand schedule for a service recipient, including the option to specify the
     * resource (e.g. a building) which fulfills on this demand.
     * @param serviceRecipientId the service recipient for whom the demand is being created
     * @return standard command result
     */
    @PostJson("/resource-demand/{serviceRecipientId}")
    @Throws(IOException::class)
    fun resourceSchedule(@PathVariable serviceRecipientId: Int, authentication: Authentication, @RequestBody requestBody: String): Result =
        resourceScheduleCommandHandler.handleCommand(authentication, serviceRecipientId, requestBody)

    @GetJson("/agreements/schedules/{scheduleId}/taskDirectHandles")
    fun getDemandScheduleDirectTaskHandles(@PathVariable scheduleId: Long): List<DemandScheduleDirectTaskHandleDto> {
        // see getAppointmentScheduleByRef
        //   - From DemandSchedule, see @PrePersist, the scheduleRef is a reference to the recurring entry, the entryHandleAsString (which is a NoteItem.uid)
        return demandScheduleDirectTaskRepository.findAllByScheduleId(scheduleId)
            .stream().map { dt: DemandScheduleDirectTask ->
                DemandScheduleDirectTaskHandleDto(
                    dt.scheduleId,
                    dt.taskInstanceId.toString(),
                )
            }
            .collect(Collectors.toList())
    }

    companion object {

        private var findOnePattern = Pattern.compile("agreements/(\\d+)/")

        @JvmField
        val EXTRACT_ID_FN = Function { href: String ->
            val matches = findOnePattern.matcher(href)
            matches.find()
            val rId = matches.group(1)
            rId.toLong()
        }

        // see also ServiceRecipientRotaDecorator, which uses getAppointmentScheduleById above
        private var findOneSchedulePattern = Pattern.compile("agreements/schedules/(\\d+)")

        @JvmField
        val EXTRACT_SCHED_ID_FN = Function { href: String ->
            val matches = findOneSchedulePattern.matcher(href)
            matches.find()
            val rId = matches.group(1)
            rId.toLong()
        }
    }
}