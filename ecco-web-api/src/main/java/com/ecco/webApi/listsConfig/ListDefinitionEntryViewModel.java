package com.ecco.webApi.listsConfig;

import com.fasterxml.jackson.annotation.JsonRawValue;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jspecify.annotations.Nullable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor // see https://github.com/rzwitserloot/lombok/issues/1389#issuecomment-546370529
public class ListDefinitionEntryViewModel {
    public Integer id;
    public String businessKey;
    public String listName;
    public String name;
    public Integer order;

    /** true if this is the default value to select when shown in a list */
    public boolean defaulted;
    public boolean disabled;

    public Integer parentId;

    public String metadata;

    /**
     * When using web-api post (data import), we use these fields to determine the hierarchy when the parents might not have an id
     */
    public String parentListName;
    public String parentItemName;

    @Nullable
    @JsonRawValue
//    @JsonInclude(value=Include.NON_NULL) // doesn't work we get "metadata": , instead of it being omitted;
    public String getMetadata() {
        return metadata;
    }

    /** see http://stackoverflow.com/a/11452577 */
    public void setMetadata(JsonNode json) {
        this.metadata = json.toString();
    }

}
