package com.ecco.webApi;

import com.ecco.buildings.repositories.AddressRepository;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.finance.webApi.dto.FinanceChargeCalculation;
import com.ecco.hr.dao.WorkerJobRepository;
import com.ecco.messaging.EmailService;
import com.ecco.messaging.MessagingConfig;
import com.ecco.repositories.finance.FinanceReceiptRepository;
import com.ecco.repositories.incidents.IncidentCommandRepository;
import com.ecco.repositories.incidents.IncidentRepository;
import com.ecco.repositories.managedvoids.ManagedVoidRepository;
import com.ecco.repositories.repairs.RepairRateRepository;
import com.ecco.repositories.repairs.RepairRepository;
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService;
import com.ecco.webApi.buildings.FixedContainerViewModelAdapter;
import com.ecco.webApi.buildings.OccupancyController;
import com.ecco.webApi.calendar.CalendarWebApiConfig;
import com.ecco.webApi.calendar.EventResourceAssembler;
import com.ecco.webApi.calendar.ServiceRecipientEventDecorator;
import com.ecco.webApi.calendar.ServiceRecipientRotaDecorator;
import com.ecco.config.repositories.ExternalSystemRepository;
import com.ecco.config.repositories.ListDefinitionRepository;
import com.ecco.config.repositories.TemplateRepository;
import com.ecco.config.service.SettingsService;
import com.ecco.config.service.SoftwareFeatureService;
import com.ecco.contacts.dao.AddressHistoryRepository;
import com.ecco.dao.*;
import com.ecco.dao.commands.*;
import com.ecco.dao.security.QueuedCommandRepository;
import com.ecco.evidence.ParentChildResolver;
import com.ecco.evidence.repositories.*;
import com.ecco.groupsupport.repositories.GroupSupportActivityInvolvementRepository;
import com.ecco.groupsupport.repositories.GroupSupportActivityRepository;
import com.ecco.hr.dao.WorkerRepository;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.config.ApplicationProperties;
import com.ecco.infrastructure.config.web.WebApiConfigBase;
import com.ecco.infrastructure.health.EndpointStats;
import com.ecco.infrastructure.hibernate.EntityUriMapper;
import com.ecco.repositories.contracts.ContractRepository;
import com.ecco.dom.contracts.RateCardCalculation;
import com.ecco.rota.service.RotaService;
import com.ecco.security.repositories.CompanyRepository;
import com.ecco.security.repositories.ContactRepository;
import com.ecco.security.repositories.IndividualRepository;
import com.ecco.security.repositories.UserRepository;
import com.ecco.security.service.UserManagementService;
import com.ecco.service.*;
import com.ecco.service.hr.HrService;
import com.ecco.service.security.CommandQueueManagerService;
import com.ecco.service.security.QueryManagerService;
import com.ecco.serviceConfig.EntityRestrictionService;
import com.ecco.serviceConfig.config.MessageSourceConfig;
import com.ecco.serviceConfig.repositories.*;
import com.ecco.serviceConfig.service.ServiceTypeService;
import com.ecco.serviceConfig.service.SessionDataService;
import com.ecco.servicerecipient.ServiceRecipientSummaryService;
import com.ecco.webApi.acls.AclController;
import com.ecco.webApi.buildings.BuildingCommandHandler;
import com.ecco.webApi.calendar.EnsureConcreteRecurrences;
import com.ecco.webApi.calendar.ItemController;
import com.ecco.webApi.clients.*;
import com.ecco.webApi.config.MSGraphConfig;
import com.ecco.webApi.contacts.*;
import com.ecco.webApi.contacts.address.AddressController;
import com.ecco.webApi.contacts.address.ServiceRecipientAddressController;
import com.ecco.webApi.contacts.address.ServiceRecipientAddressLocationChangeCommandHandler;
import com.ecco.webApi.controllers.*;
import com.ecco.webApi.email.EmailCommandHandler;
import com.ecco.webApi.encrypted.SecureCommandProxy;
import com.ecco.webApi.encrypted.SecureQueryProxy;
import com.ecco.webApi.evidence.*;
import com.ecco.webApi.featureConfig.ListDefCommandHandler;
import com.ecco.webApi.featureConfig.ListDefinitionEntryIdToViewModel;
import com.ecco.webApi.featureConfig.SessionDataController;
import com.ecco.webApi.finance.FinanceReceiptController;
import com.ecco.webApi.finance.FinanceReceiptCommandHandler;
import com.ecco.webApi.finance.FinanceReceiptListRowResourceAssembler;
import com.ecco.webApi.finance.FinanceService;
import com.ecco.webApi.forms.FormDefinitionController;
import com.ecco.webApi.groupSupport.GroupActivityCommandHandler;
import com.ecco.webApi.incidents.*;
import com.ecco.webApi.listsConfig.AclListsController;
import com.ecco.webApi.managedvoids.CreateManagedVoidCommandHandler;
import com.ecco.webApi.managedvoids.ManagedVoidController;
import com.ecco.webApi.managedvoids.ManagedVoidFromViewModel;
import com.ecco.webApi.messaging.CalendarNotificationAgent;
import com.ecco.webApi.monitoring.Log4j2LevelController;
import com.ecco.webApi.notifications.NotificationController;
import com.ecco.webApi.monitoring.StatsController;
import com.ecco.webApi.portal.PortalController;
import com.ecco.webApi.repairs.CreateRepairCommandHandler;
import com.ecco.webApi.repairs.RepairController;
import com.ecco.webApi.repairs.RepairFromViewModel;
import com.ecco.webApi.report.ReportDefinitionController;
import com.ecco.webApi.rota.*;
import com.ecco.webApi.serviceConfig.ActionDefController;
import com.ecco.webApi.serviceConfig.ServicesService;
import com.ecco.webApi.singleValue.SingleValueHistoryCommandController;
import com.ecco.webApi.support.EtaggedResponseCacheManager;
import com.ecco.webApi.taskFlow.ReferralTaskAcceptOnServiceCommandHandler;
import com.ecco.webApi.tasks.TaskStatusListController;
import com.ecco.webApi.tunnel.HttpTunnelSocket;
import com.ecco.webApi.tunnel.WebSocketConfig;
import com.ecco.webApi.upload.AvatarController;
import com.ecco.webApi.users.UserAccessAuditCommandHandler;
import com.ecco.webApi.users.UserCommandController;
import com.ecco.webApi.users.UserCoreCommandHandler;
import com.ecco.webApi.users.UserListRowResourceAssembler;
import com.ecco.webApi.workers.WorkerListController;
import com.ecco.webApi.workers.WorkerListRowResourceAssembler;
import com.ecco.workflow.activiti.ActivitiWorkflowServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ecco.calendar.core.CalendarService;
import com.ecco.calendar.core.webapi.EventResource;
import com.ecco.security.config.AclConfig;
import com.ecco.security.dao.acl.AclEntryRepository;
import com.ecco.security.dao.acl.AclObjectIdentityRepository;
import com.ecco.security.acl.AclHandler;
import org.activiti.engine.RepositoryService;
import org.jspecify.annotations.NonNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.CacheManager;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.MessageSourceAccessor;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.util.PathMatcher;

import javax.sql.DataSource;

@Import({
        AclConfig.class,
        MessagingConfig.class,
        CalendarWebApiConfig.class,
        MessageSourceConfig.class,
        MSGraphConfig.class,
        WebSocketConfig.class
})
@ComponentScan( basePackageClasses = {
        ReferralTaskAcceptOnServiceCommandHandler.class,
        BuildingCommandHandler.class,
        GroupActivityCommandHandler.class,
        AclListsController.class,
        ActionDefController.class,
        FinanceReceiptController.class,
        FinanceChargeCalculation.class,
        FormDefinitionController.class,
        InboundBaseController.class,
        PortalController.class,
        SessionDataController.class,
        SingleValueHistoryCommandController.class,
        RotaActivityInvoiceController.class,
        TaskStatusListController.class,
        UserCoreCommandHandler.class,
        UserCommandController.class,
        WorkerListController.class,
        IncidentController.class,
        RepairController.class,
        ManagedVoidController.class,
        AvatarController.class,
        CalendarNotificationAgent.class,
        NotificationController.class,
        OccupancyController.class
    })
@EnableScheduling
@Qualifier("apiControllerMapping")
@Order(50)
public class WebApiConfig extends WebApiConfigBase {

    @Bean
    public EccoWebApiControllerAdvice apiControllerAdvice() {
        return new EccoWebApiControllerAdvice();
    }

    @Bean
    public MigrateCustomPropertiesToEvidenceForm migrateCustomPropertiesOnStartup(FormEvidenceMigration formEvidenceMigration,
                                                                  ReferralRepository referralRepository,
                                                                  EvidenceFormSnapshotRepository evidenceFormSnapshotRepository) {
        return new MigrateCustomPropertiesToEvidenceForm(formEvidenceMigration, referralRepository, evidenceFormSnapshotRepository);
    }

    /*@Bean
    @ConditionalOnProperty("migrateAccommodationToBuildings")
    public MigrateAccommodationToBuildings migrateAccommodationToBuildings(
            FixedContainerRepository fixedContainerRepository,
            ProjectRepository projectRepository,
            PlatformTransactionManager txManager) {
        return new MigrateAccommodationToBuildings(fixedContainerRepository, projectRepository, txManager);
    }*/

    @Bean
    @ConditionalOnProperty("fixConcreteRefs")
    public EnsureConcreteRecurrences ensureConcreteRecurrences(
            CalendarService calendarService,
            PlatformTransactionManager txManager) {
        return new EnsureConcreteRecurrences(calendarService, txManager);
    }

    @Bean
    public CalendarNotificationAgent calendarNotificationAgent(CalendarService calendarService,
                                                               EmailService emailService,
                                                               TemplateRepository templateRepository,
                                                               SoftwareFeatureService featureService,
                                                               MessageBus<ApplicationEvent> messageBus) {
        return new CalendarNotificationAgent(calendarService, emailService, featureService, templateRepository, messageBus);
    }

    @Bean
    public ActivitiProcessDefinitionController processDefinitionController(RepositoryService repositoryService) {
        return new ActivitiProcessDefinitionController(repositoryService);
    }

    @Bean
    AddressController addressController(AddressService addressService, AddressRepository addressRepository) {
        return new AddressController(addressService, addressRepository);
    }

    @Bean
    public ServiceRecipientAddressController serviceRecipientAddressController(ServiceRecipientAddressLocationChangeCommandHandler serviceRecipientAddressLocationChangeCommandHandler,
                                                                               AddressHistoryRepository addressHistoryRepository) {
        return new ServiceRecipientAddressController(serviceRecipientAddressLocationChangeCommandHandler, addressHistoryRepository);
    }


    @Bean
    public ContactCalendarEntryCommandHandler contactCalendarEntryCommandHandler(
            @NonNull CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler,
            @NonNull ObjectMapper objectMapper,
            @NonNull ContactsCommandRepository contactsCommandRepository,
            @NonNull EntityUriMapper entityUriMapper) {
        return new ContactCalendarEntryCommandHandler(calendarEntryCommandSupportHandler, objectMapper,
                contactsCommandRepository, entityUriMapper);
    }

    @Bean
    public ContactCommandHandler contactCommandHandler(
            @NonNull ObjectMapper objectMapper,
            @NonNull ContactRepository contactRepository,
            @NonNull ContactsCommandRepository contactsCommandRepository) {
        return new ContactCommandHandler(objectMapper, contactsCommandRepository,
                ContactCommandViewModel.class, contactRepository);
    }

    @Bean
    public ContactAddressCommandHandler contactAddressCommandHandler(
            @NonNull ObjectMapper objectMapper,
            @NonNull ContactRepository contactRepository,
            @NonNull ContactsCommandRepository contactsCommandRepository) {
        return new ContactAddressCommandHandler(objectMapper, contactsCommandRepository,
                ContactAddressCommandViewModel.class, contactRepository);
    }

    @Bean
    public ContactsController contactsController(ContactRepository contactRepository) {
        return new ContactsController(contactRepository);
    }

    @Bean
    public ContactsCommandController contactsCommandController(ContactsCommandRepository commandRepository,
                                                 @NonNull ContactCalendarEntryCommandHandler contactCalendarEntryCommandHandler,
                                                 @NonNull ContactCommandHandler contactCommandHandler,
                                                 @NonNull ContactAddressCommandHandler contactAddressCommandHandler,
                                                 ExtractCommandViewModelJson extractJsonBody) {
        return new ContactsCommandController(commandRepository, contactCalendarEntryCommandHandler,
                                             contactCommandHandler, contactAddressCommandHandler, extractJsonBody);
    }

    @Bean
    public CompanyController companyController(CompanyRepository companyRepository,
                                               IndividualRepository individualRepository,
                                               ReferralRepository referralRepository) {
        return new CompanyController(companyRepository, individualRepository, referralRepository);
    }

    @Bean
    public AgencyController agencyController(AgencyRepository agencyRepository,
                                             AddressRepository addressRepository,
                                             ContactAllocationRepository contactAllocationRepository,
                                             ContactService contactService,
                                             ServiceRecipientRepository serviceRecipientRepository) {
        return new AgencyController(agencyRepository, addressRepository, contactAllocationRepository,
                contactService, serviceRecipientRepository);
    }

    @Bean
    @Primary
    AgreementResourceAssembler agreementResourceAssembler(ContractRepository contractRepository, DemandScheduleRepository demandScheduleRepository) {
        return new AgreementResourceAssembler(contractRepository, demandScheduleRepository, true);
    }

    @Bean
    AgreementResourceAssembler agreementResourceAssemblerNoSchedules(ContractRepository contractRepository, DemandScheduleRepository demandScheduleRepository) {
        return new AgreementResourceAssembler(contractRepository, demandScheduleRepository, false);
    }

/*
    @Bean
    public CalendarEventSnapshotToDto calendarEventSnapshotToDto(ServiceRecipientRepository serviceRecipientRepository) {
        return new CalendarEventSnapshotToDto(serviceRecipientRepository);
    }
*/

    @Bean
    public SchedulerListRowResourceAssembler schedulerListRowResourceAssembler(CalendarEventSnapshotToDto calendarEventSnapshotToDto) {
        return new SchedulerListRowResourceAssembler(calendarEventSnapshotToDto);
    }

    @Bean
    ClientSalesInvoiceResourceAssembler clientSalesInvoiceResourceAssembler() {
        return new ClientSalesInvoiceResourceAssembler();
    }

    @Bean
    ClientSalesRotaInvoiceDetailResourceAssembler clientSalesInvoiceDetailResourceAssembler(ClientSalesRotaInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler) {
        return new ClientSalesRotaInvoiceDetailResourceAssembler(clientSalesInvoiceDetailResourceLineAssembler);
    }

    @Bean
    ClientSalesRotaInvoiceDetailResourceLineAssembler clientSalesInvoiceDetailResourceLineAssembler(RotaService rotaService,
                                                                                                    EventService eventService,
                                                                                                    RateCardCalculation rateCardCalculation) {
        return new ClientSalesRotaInvoiceDetailResourceLineAssembler(rotaService, eventService, rateCardCalculation);
    }

    @Bean
    ClientFromViewModel clientFromViewModel(IdNameService idNameService, AddressRepository addressRepository,
                                            ListDefinitionRepository listDefinitionRepository) {
        return new ClientFromViewModel(idNameService, addressRepository, listDefinitionRepository);
    }

    @Bean
    ClientToViewModel clientToViewModel(ListDefinitionRepository listDefinitionRepository) {
        return new ClientToViewModel(listDefinitionRepository);
    }

    @Bean
    IncidentFromViewModel incidentFromViewModel(ServiceCategorisationRepository serviceCategorisationRepository) {
        return new IncidentFromViewModel(serviceCategorisationRepository);
    }

    @Bean
    RepairFromViewModel repairFromViewModel(ServiceCategorisationRepository serviceCategorisationRepository) {
        return new RepairFromViewModel(serviceCategorisationRepository);
    }

    @Bean
    ManagedVoidFromViewModel managedVoidFromViewModel() {
        return new ManagedVoidFromViewModel();
    }

    @Bean
    IndividualFromViewModel individualFromViewModel(AddressRepository addressRepository) {
        return new IndividualFromViewModel(addressRepository);
    }

    @Bean
    IndividualUserSummaryToViewModel individualUserSummaryToViewModel(CalendarService calendarService) {
        return new IndividualUserSummaryToViewModel(calendarService);
    }

    @Bean
    ClientWebService clientWebService(ClientRepository clientRepository, ReferralService referralService,
                                      ClientCommandRepository clientCommandRepository, ObjectMapper objectMapper,
                                      ClientFromViewModel clientFromViewModel,
                                      IncidentRepository incidentRepository, IncidentFromViewModel incidentFromViewModel,
                                      IncidentCommandRepository incidentCommandRepository) {
        return new ClientWebService(objectMapper, clientRepository, clientCommandRepository, referralService, clientFromViewModel,
                incidentRepository, incidentFromViewModel, incidentCommandRepository);
    }

    @Bean
    public ClientController clientController(ClientRepository clientRepository, ClientDetailService clientDetailService,
                                             ClientFromViewModel clientFromViewModel, ClientToViewModel clientToViewModel,
                                             ClientWebService clientWebService, ListDefinitionRepository listDefinitionRepository) {
        return new ClientController(clientRepository, clientDetailService, clientFromViewModel, clientToViewModel,
                clientWebService, listDefinitionRepository);
    }

    @Bean
    public ClientUpdateCommandController clientUpdateCommandController(ClientRepository clientRepository,
                                                                       ClientAttributeChangeCommandHandler handler,
                                                                       ExternalSystemRepository externalSystemRepository) {
        return new ClientUpdateCommandController(clientRepository, handler, externalSystemRepository);
    }

    @Bean
    ClientAttributeChangeCommandHandler clientAttributeChangeCommandHandler(
            ObjectMapper objectMapper, ClientCommandRepository commandRepository,
            ClientRepository clientRepository) {
        return new ClientAttributeChangeCommandHandler(objectMapper, commandRepository, clientRepository);
    }

    @Bean
    CommandQueueController commandQueueController(CommandQueueManagerService commandQueueManagerService,
                                                  QueuedCommandRepository queuedCommandRepository) {
        return new CommandQueueController(commandQueueManagerService, queuedCommandRepository);
    }

    @Bean
    public ExtractCommandViewModelJson extractJsonBody(UserRepository userRepository) {
        return new ExtractCommandViewModelJson(userRepository);
    }

    @Bean(name = "SvcRecExtractJson")
    public GoalCommandExtractCommandViewModelJson goalCommandExtractJsonBody(UserRepository userRepository) {
        return new GoalCommandExtractCommandViewModelJson(userRepository);
    }

    @Bean(name = "SvcRecExtractJsonWithDisplayName")
    public ServiceRecipientExtractCommandViewModelJson referralServiceRecipientExtractJsonBody(UserRepository userRepository) {
        return new ServiceRecipientExtractCommandViewModelJson(userRepository);
    }

    @Bean
    public ExtractTaskCommandViewModelJson extractTaskCommandAsJsonBody(UserRepository userRepository) {
        return new ExtractTaskCommandViewModelJson(userRepository);
    }

    @Bean
    public FinanceReceiptListRowResourceAssembler financeReceiptListRowResourceAssembler() {
        return new FinanceReceiptListRowResourceAssembler();
    }

    @Bean
    FinanceReceiptCommandHandler financeReceiptCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ServiceRecipientRepository serviceRecipientRepository, FinanceReceiptRepository financeReceiptRepository) {
        return new FinanceReceiptCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceRecipientRepository, financeReceiptRepository);
    }

    @Bean
    public UserAccessAuditCommandHandler userAccessAuditCommandHandler(
            ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository) {
        return new UserAccessAuditCommandHandler(objectMapper, serviceRecipientCommandRepository);
    }

    @Bean
    public EmailCommandHandler emailCommandHandler(
            ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ServiceRepository serviceRepository,
            EmailService emailService,
            TaskDefinitionService taskDefinitionService) {
        return new EmailCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceRecipientRepository,
                serviceRepository, emailService, taskDefinitionService);
    }

    @Bean
    public RiskAreaCommandHandler riskAreaCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            RiskAreaEvidenceRepository areaRepository, ThreatWorkRepository threatWorkRepository,
            ClientRepository clientRepository,
            ServiceRepository serviceRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            TaskDefinitionService taskDefinitionService,
            ParentChildResolver parentChildResolver,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService) {
        return new RiskAreaCommandHandler(objectMapper, serviceRecipientRepository, serviceRecipientCommandRepository,
                serviceRepository, areaRepository, threatWorkRepository, clientRepository, taskDefinitionService,
                parentChildResolver, entityUriMapper, calendarService);
    }

    @Bean
    public RiskGoalCommandHandler riskGoalCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ThreatActionRepository threatActionRepository, ThreatWorkRepository threatWorkRepository,
            ClientRepository clientRepository,
            ServiceRepository serviceRepository,
            ParentChildResolver parentChildResolver,
            ServiceRecipientRepository serviceRecipientRepository,
            CalendarService calendarService,
            TaskDefinitionService taskDefinitionService,
            EntityUriMapper entityUriMapper,
            ThreatWorkActionAssociationRepository riskWorkActionAssociationRepository) {
        return new RiskGoalCommandHandler(objectMapper, serviceRecipientCommandRepository, threatActionRepository,
                threatWorkRepository, serviceRecipientRepository, serviceRepository,
                clientRepository, parentChildResolver, calendarService, taskDefinitionService,
                entityUriMapper, riskWorkActionAssociationRepository);
    }

    @Bean
    public GoalCommandHandler goalCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            EvidenceSupportActionRepository threatActionRepository, EvidenceSupportWorkRepository supportWorkRepository,
            ClientRepository clientRepository, ServiceRecipientRepository serviceRecipientRepository,
            SupportWorkActionAssociationRepository swaaRepository,
            ServiceRepository serviceRepository,
            ParentChildResolver parentChildResolver,
            ListDefinitionRepository listDefRepository,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService,
            TaskDefinitionService taskDefinitionService) {
        return new GoalCommandHandler(objectMapper, serviceRecipientCommandRepository, threatActionRepository,
                supportWorkRepository, clientRepository, serviceRecipientRepository, swaaRepository,
                serviceRepository, parentChildResolver, listDefRepository, entityUriMapper, calendarService, taskDefinitionService);
    }

    @Bean
    public QuestionAnswerCommandHandler questionAnswerCommandHandler(
            ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            EvidenceQuestionAnswerRepository questionAnswerRepository,
            ServiceRepository serviceRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            TaskDefinitionService taskDefinitionService,
            ParentChildResolver parentChildResolver,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService) {
        return new QuestionAnswerCommandHandler(objectMapper,
                serviceRecipientCommandRepository,
                supportWorkRepository, questionAnswerRepository, serviceRepository,
                serviceRecipientRepository,
                parentChildResolver, entityUriMapper, calendarService, taskDefinitionService);
    }

    @Bean
    public RiskCommentCommandHandler riskCommentCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ThreatWorkRepository threatWorkRepository,
            ThreatCommentRepository threatCommentRepository,
            ClientRepository clientRepository,
            ServiceRepository serviceRepository,
            EvidenceSupportCommentRepository supportCommentRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            TaskDefinitionService taskDefinitionService,
            ParentChildResolver parentChildResolver,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService) {
        return new RiskCommentCommandHandler(objectMapper, serviceRecipientCommandRepository,
                threatWorkRepository, threatCommentRepository, serviceRecipientRepository, clientRepository,
                supportCommentRepository, serviceRepository, parentChildResolver, entityUriMapper, calendarService,
                taskDefinitionService);
    }

    @Bean
    public SupportCommentCommandHandler supportCommentCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            EvidenceSupportCommentRepository supportCommentRepository,
            ClientRepository clientRepository,
            ReviewRepository reviewRepository,
            ServiceRepository serviceRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            TaskDefinitionService taskDefinitionService,
            ParentChildResolver parentChildResolver,
            CalendarService calendarService,
            EventService eventService,
            EntityUriMapper entityUriMapper,
            CustomEventRepository eventRepository) {
        return new SupportCommentCommandHandler(objectMapper, serviceRecipientCommandRepository,
                supportWorkRepository, supportCommentRepository, clientRepository,
                reviewRepository, serviceRepository,
                serviceRecipientRepository, taskDefinitionService, parentChildResolver,
                calendarService, eventService, entityUriMapper, eventRepository);
    }

    @Bean
    public QuestionnaireCommentCommandHandler questionnaireCommentCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            ClientRepository clientRepository,
            ServiceRepository serviceRepository,
            EvidenceSupportCommentRepository supportCommentRepository,
            ReviewRepository reviewRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ParentChildResolver parentChildResolver,
            CalendarService calendarService,
            EventService eventService,
            EntityUriMapper entityUriMapper,
            CustomEventRepository eventRepository,
            TaskDefinitionService taskDefinitionService) {
        return new QuestionnaireCommentCommandHandler(objectMapper, serviceRecipientCommandRepository,
                supportWorkRepository, supportCommentRepository, clientRepository,
                reviewRepository, serviceRecipientRepository, serviceRepository, parentChildResolver, calendarService,
                eventService, entityUriMapper, eventRepository, taskDefinitionService);
    }

    @Bean
    public EvidenceFormCommentCommandHandler customFormCommentCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            TaskDefinitionService taskDefinitionService,
            EvidenceFormWorkRepository workRepository,
            EvidenceFormCommentRepository commentRepository,
            ClientRepository clientRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ServiceRepository serviceRepository,
            ParentChildResolver parentChildResolver,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService) {
        return new EvidenceFormCommentCommandHandler(objectMapper, serviceRecipientCommandRepository,
                taskDefinitionService, workRepository, commentRepository, clientRepository,
                serviceRecipientRepository, serviceRepository, parentChildResolver, entityUriMapper, calendarService);
    }

    @Bean
    public ReportDefinitionController reportDefinitionController(
            ReportDefinitionRepository reportDefinitionRepository,
            ReportDefinitionCommandRepository reportDefinitionCommandRepository,
            ObjectMapper objectMapper) {
        return new ReportDefinitionController(reportDefinitionRepository, reportDefinitionCommandRepository, objectMapper);
    }

    @Bean
    ListDefCommandHandler listDefCommandHandler(
            ObjectMapper objectMapper,
            ConfigCommandRepository configCommandRepository,
            ListDefinitionRepository listDefinitionRepository) {
        return new ListDefCommandHandler(objectMapper,
                listDefinitionRepository, configCommandRepository);
    }

    @Bean
    public ListDefinitionEntryIdToViewModel listDefinitionEntryIdToViewModel(
            ListDefinitionRepository listDefinitionRepository) {
        return new ListDefinitionEntryIdToViewModel(listDefinitionRepository);
    }

    @Bean
    public EtaggedResponseCacheManager etaggedResponseCacheManager(CacheManager cacheManager,
                                                                   ObjectMapper objectMapper) {
        return new EtaggedResponseCacheManager(objectMapper, cacheManager);
    }

    @Bean
    public IndividualController individualController(IndividualRepository individualRepository,
                                                     AddressRepository addressRepository,
                                                     UserManagementService userManagementService,
                                                     ContactAllocationRepository contactAllocationRepository,
                                                     ContactService contactService,
                                                     IndividualFromViewModel individualFromViewModel,
                                                     IndividualUserSummaryToViewModel individualUserSummaryToViewModel) {
        return new IndividualController(individualRepository, addressRepository, userManagementService, contactAllocationRepository,
                contactService, individualFromViewModel, individualUserSummaryToViewModel);
    }

    @Bean
    public ItemController itemController(CalendarService calendarService, EventService eventService) {
        return new ItemController(calendarService, eventService);
    }

    @Bean("referralEventAssembler")
    public EventResourceAssembler eventResourceAssembler(
                                                 ReferralRepository referralRepository,
                                                 FixedContainerRepository fixedContainerRepository,
                                                 EntityUriMapper entityUriMapper,
                                                 ServiceRecipientRepository serviceRecipientRepository,
                                                 SoftwareFeatureService softwareFeatureService,
                                                 ServiceRecipientSummaryService serviceRecipientSummaryService,
                                                 DemandScheduleRepository demandScheduleRepository,
                                                 ApplicationProperties appConfig) {

        var rotaDecorator = new ServiceRecipientRotaDecorator<EventResource>(referralRepository, fixedContainerRepository,
                serviceRecipientSummaryService, serviceRecipientRepository, demandScheduleRepository, appConfig);

        ServiceRecipientEventDecorator decorator = new ServiceRecipientEventDecorator(entityUriMapper,
                referralRepository, serviceRecipientRepository, serviceRecipientSummaryService, softwareFeatureService, rotaDecorator, appConfig);
        return new EventResourceAssembler(decorator, ReferralController.class);
    }

    @Bean
    public ServiceRecipientAssociatedContactController serviceRecipientAssociatedContactController(
                                                ContactRepository contactRepository,
                                                ServiceRecipientContactRepository serviceRecipientContactRepository) {
        return new ServiceRecipientAssociatedContactController(contactRepository, serviceRecipientContactRepository);
    }

    @Bean
    public ServiceRecipientAssociatedContactCommandHandler serviceRecipientAssociatedContactCommandHandler(ObjectMapper objectMapper,
                                                                                                   ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                                   ServiceRecipientContactRepository serviceRecipientContactRepository,
                                                                                                   ReferralRepository referralRepository) {
        return new ServiceRecipientAssociatedContactCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceRecipientContactRepository, referralRepository);
    }

    @Bean
    public ServiceRecipientAssociatedContactCommandController serviceRecipientAssociatedContactCommandController(
                                                ServiceRecipientAssociatedContactCommandHandler handler) {
        return new ServiceRecipientAssociatedContactCommandController(handler);
    }

    @Bean
    public CreateReferralCommandHandler createReferralCommandHandler(ObjectMapper objectMapper,
                                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                     FundingSourceRepository fundingSourceRepository,
                                                                     LocalAuthorityRepository localAuthorityRepository,
                                                                     ReferralRepository referralRepository, ServiceRepository serviceRepository, ProjectRepository projectRepository,
                                                                     ServiceCategorisationRepository serviceCategorisationRepository,
                                                                     IndividualRepository individualRepository,
                                                                     ListDefinitionRepository listDefinitionRepository,
                                                                     WorkflowController workflowController,
                                                                     SignatureRepository signatureRepository,
                                                                     ClientRepository clientRepository) {
        return new CreateReferralCommandHandler(objectMapper, serviceRecipientCommandRepository,
                fundingSourceRepository, localAuthorityRepository, referralRepository, serviceRepository, projectRepository,
                serviceCategorisationRepository,
                individualRepository,
                listDefinitionRepository, workflowController, signatureRepository,
                clientRepository);
    }

    @Bean
    public CreateWorkerJobCommandHandler createWorkerJobCommandHandler(ObjectMapper objectMapper,
                                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                     ServiceCategorisationRepository serviceCategorisationRepository,
                                                                     WorkerJobRepository workerJobRepository) {
        return new CreateWorkerJobCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceCategorisationRepository, workerJobRepository);
    }

    @Bean
    public CreateIncidentCommandHandler createIncidentCommandHandler(ObjectMapper objectMapper,
                                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                     IncidentRepository incidentRepository,
                                                                     IncidentFromViewModel incidentFromViewModel) {
        return new CreateIncidentCommandHandler(objectMapper, serviceRecipientCommandRepository, incidentRepository, incidentFromViewModel);
    }

    @Bean
    public CreateRepairCommandHandler createRepairCommandHandler(ObjectMapper objectMapper,
                                                                   ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                   RepairRepository repairRepository,
                                                                   RepairFromViewModel repairFromViewModel) {
        return new CreateRepairCommandHandler(objectMapper, serviceRecipientCommandRepository, repairRepository, repairFromViewModel);
    }

    @Bean
    public CreateManagedVoidCommandHandler createManagedVoidCommandHandler(ObjectMapper objectMapper,
                                                                           ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                           ManagedVoidRepository managedVoidRepository,
                                                                           ManagedVoidFromViewModel managedVoidFromViewModel) {
        return new CreateManagedVoidCommandHandler(objectMapper, serviceRecipientCommandRepository, managedVoidRepository, managedVoidFromViewModel);
    }

    @Bean
    public CreateServiceRecipientCommandController createServiceRecipientCommandController(
            CreateReferralCommandHandler createReferralCommandHandler,
            CreateWorkerJobCommandHandler createWorkerJobCommandHandler,
            CreateIncidentCommandHandler createIncidentCommandHandler,
            CreateRepairCommandHandler createRepairCommandHandler,
            CreateManagedVoidCommandHandler createManagedVoidCommandHandler) {
        return new CreateServiceRecipientCommandController(createReferralCommandHandler, createWorkerJobCommandHandler,
                createIncidentCommandHandler, createRepairCommandHandler, createManagedVoidCommandHandler);
    }

    @Bean
    public MoveServiceRecipientCommandHandler moveServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                                                                 ReferralRepository referralRepository,
                                                                                 ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                 ServiceRecipientRepository serviceRecipientRepository) {
        return new MoveServiceRecipientCommandHandler(objectMapper, referralRepository, serviceRecipientCommandRepository, serviceRecipientRepository);
    }

    @Bean
    public DeleteRequestServiceRecipientCommandHandler deleteRequestServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                                                                                   ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                                   ReferralService referralService) {
        return new DeleteRequestServiceRecipientCommandHandler(objectMapper, serviceRecipientCommandRepository, referralService);
    }

    @Bean
    public DeleteServiceRecipientCommandHandler deleteServiceRecipientCommandHandler(ObjectMapper objectMapper,
                                                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                     ServiceRecipientRepository serviceRecipientRepository) {
        return new DeleteServiceRecipientCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceRecipientRepository);
    }

    @Bean
    public DeleteRequestEvidenceCommandHandler deleteRequestEvidenceCommandHandler(MessageBus<ApplicationEvent> messageBus, ObjectMapper objectMapper,
                                                                                   ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                   EvidenceSupportWorkRepository supportWorkRepository,
                                                                                   EvidenceFormWorkRepository formWorkRepository,
                                                                                   ThreatWorkRepository riskWorkRepository,
                                                                                   ServiceRecipientRepository serviceRecipientRepository) {
        return new DeleteRequestEvidenceCommandHandler(messageBus, objectMapper, serviceRecipientCommandRepository, supportWorkRepository, formWorkRepository, riskWorkRepository, serviceRecipientRepository);
    }

    @Bean
    public DeleteEvidenceCommandHandler deleteEvidenceCommandHandler(ObjectMapper objectMapper,
                                                                     ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                     EvidenceSupportWorkRepository supportWorkRepository,
                                                                     ThreatWorkRepository riskWorkRepository,
                                                                     GroupSupportActivityInvolvementRepository groupSupportRepository,
                                                                     TaskDefinitionService taskDefinitionService) {
        return new DeleteEvidenceCommandHandler(objectMapper,
                serviceRecipientCommandRepository,
                supportWorkRepository,
                riskWorkRepository,
                groupSupportRepository,
                taskDefinitionService);
    }

    @Bean
    public ServiceRecipientAddressLocationChangeCommandHandler serviceRecipientAddressLocationChangeCommandHandler(
            ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ContactService contactService,
            AddressRepository addressRepository,
            AddressHistoryRepository addressHistoryRepository,
            ClientRepository clientRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            FixedContainerRepository buildingRepository,
            ServiceRecipientSummaryService serviceRecipientSummaryService) {
        return new ServiceRecipientAddressLocationChangeCommandHandler(objectMapper, serviceRecipientCommandRepository,
                contactService, addressRepository, addressHistoryRepository, clientRepository, serviceRecipientRepository,
                buildingRepository, serviceRecipientSummaryService);
    }

    @Bean
    public CalendarEntryCommandHandlerSupport calendarEntryCommandHandlerSupport(
            @NonNull EventService eventService,
            @NonNull IndividualRepository individualRepository,
            @NonNull ServiceRecipientRepository serviceRecipientRepository,
            @NonNull CalendarService calendarService,
            @NonNull CustomEventRepository customEventNonRecurringRepository,
            @NonNull CustomEventRecurringRepository customEventRecurringRepository,
            @NonNull EntityUriMapper entityUriMapper) {
        return new CalendarEntryCommandHandlerSupport(eventService, individualRepository,
                serviceRecipientRepository, calendarService,
                customEventNonRecurringRepository, customEventRecurringRepository, entityUriMapper);
     }

    @Bean
    public ServiceRecipientCalendarEntryCommandHandler serviceRecipientCalendarEntryCommandHandler(
            @NonNull CalendarEntryCommandHandlerSupport calendarEntryCommandSupportHandler,
            @NonNull ObjectMapper objectMapper,
            @NonNull ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            @NonNull ServiceRecipientRepository serviceRecipientRepository,
            @NonNull EntityUriMapper entityUriMapper) {
        return new ServiceRecipientCalendarEntryCommandHandler(calendarEntryCommandSupportHandler, objectMapper, serviceRecipientCommandRepository, serviceRecipientRepository, entityUriMapper);
    }

    @Bean
    public EvidenceFormSnapshotCommandHandler jsonPatchCommandHandler(ObjectMapper objectMapper,
                                                                      ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                      ServiceRecipientRepository serviceRecipientRepository,
                                                                      EvidenceFormWorkRepository evidenceFormWorkRepository,
                                                                      EvidenceFormSnapshotRepository evidenceFormSnapshotRepository,
                                                                      TaskDefinitionService taskDefinitionService) {
        return new EvidenceFormSnapshotCommandHandler(objectMapper,
                            serviceRecipientCommandRepository,
                            serviceRecipientRepository,
                            evidenceFormWorkRepository,
                            evidenceFormSnapshotRepository,
                taskDefinitionService);
    }

    @Bean
    public FormEvidenceMigration formEvidenceMigration(ReferralService referralService, ReferralRepository referralRepository,
                                                       EvidenceFormWorkController controller, ListDefinitionRepository listDefinitionRepository,
                                                       ObjectMapper objectMapper) {
        return new FormEvidenceMigration(referralService, referralRepository, controller, listDefinitionRepository, objectMapper);
    }

    @Bean
    public ReferralChangeCommandController referralUpdateCommandController(ReferralRepository referralRepository) {
        return new ReferralChangeCommandController(referralRepository);
    }

    @Bean
    public ServiceRecipientAttributeChangeCommandHandler serviceRecipientAttributeChangeCommandHandler(ObjectMapper objectMapper,
                                                                                                       ServiceRecipientCommandRepository serviceRecipientCommandRepository,
                                                                                                       ServiceRecipientRepository serviceRecipientRepository) {
        return new ServiceRecipientAttributeChangeCommandHandler(objectMapper, serviceRecipientCommandRepository, serviceRecipientRepository);
    }

    @Bean
    public ServiceRecipientAttributeChangeCommandController serviceRecipientAttributeChangeCommandController(ServiceRecipientAttributeChangeCommandHandler attributeChangeCommandHandler) {
        return new ServiceRecipientAttributeChangeCommandController(attributeChangeCommandHandler);
    }

    @Bean
    public ReportUnsecuredDelegator reportDelegator(ServiceTypeService serviceTypeService,
                                                    ReferralRepository referralRepository,
                                                    RepositoryBasedServiceCategorisationService serviceCategorisationService,
                                                    TaskDefinitionService taskDefinitionService,
                                                    ListDefinitionRepository listDefinitionRepository,
                                                    IndividualRepository individualRepository,
                                                    RepairRateRepository repairRateRepository,
                                                    GroupSupportActivityRepository activityRepository,
                                                    EntityRestrictionService entityRestrictionService) {
        return new ReportUnsecuredDelegator(new ReferralSummaryToViewModel(),
                new ReferralToViewModel(listDefinitionRepository),
                referralRepository,
                activityRepository,
                entityRestrictionService,
                serviceCategorisationService,
                taskDefinitionService,
                listDefinitionRepository,
                repairRateRepository,
                individualRepository);
    }

    @Bean
    public ReportController reportController(
            EntityRestrictionService entityRestrictionService,
            FinanceService financeService,
            ClientRepository clientRepository,
            ReferralRepository referralRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            EvidenceSupportCommentRepository supportCommentRepository,
            EvidenceSupportAnswerRepository supportAnswerRepository,
            QuestionnaireWorkRepository questionnaireWorkRepository,
            EvidenceQuestionAnswerRepository questionAnswerRepository,
            GroupSupportActivityInvolvementRepository involvementRepository,
            AgencyRepository agencyRepository,
            IndividualRepository individualRepository,
            ContactRepository contactRepository,
            SettingsService settingsService,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            TaskStatusRepository taskStatusRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            UserRepository userRepository,
            EvidenceFormSnapshotRepository evidenceFormSnapshotRepository,
            FixedContainerRepository buildingRepository,
            FixedContainerViewModelAdapter fixedContainerViewModelAdapter,
            ServiceTypeService serviceTypeService,
            ServiceCategorisationRepository serviceCategorisationRepository,
            RepositoryBasedServiceCategorisationService serviceCategorisationService,
            TaskDefinitionService taskDefinitionService,
            ClientToViewModel clientToViewModel,
            @Qualifier("SvcRecExtractJsonWithDisplayName") ServiceRecipientExtractCommandViewModelJson extractJsonBody,
            ExtractCommandViewModelJson baseExtractJsonBody,
            @Qualifier("referralEventAssembler") EventResourceAssembler eventResourceAssembler,
            ReportUnsecuredDelegator reportUnsecuredDelegator,
            ListDefinitionRepository listDefinitionRepository,
            RepairRateRepository repairRateRepository,
            ObjectMapper objectMapper,
            AclConfig aclConfig,
            ApplicationProperties appProps) {
        return new ReportController(entityRestrictionService, financeService, clientRepository,
                referralRepository, supportWorkRepository, supportCommentRepository, supportAnswerRepository, questionnaireWorkRepository, questionAnswerRepository,
                involvementRepository, agencyRepository, individualRepository, contactRepository, settingsService, serviceRecipientCommandRepository,
                taskStatusRepository, serviceRecipientRepository, userRepository, evidenceFormSnapshotRepository, buildingRepository, serviceTypeService,
                serviceCategorisationRepository,
                serviceCategorisationService,
                taskDefinitionService,
                clientToViewModel, extractJsonBody, baseExtractJsonBody, eventResourceAssembler, reportUnsecuredDelegator,
                listDefinitionRepository,
                fixedContainerViewModelAdapter,
                repairRateRepository,
                objectMapper,
                aclConfig,
                appProps);
    }

    @Bean
    public SignWorkCommandHandler signWorkCommandHandler(ObjectMapper objectMapper,
            ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            ClientRepository clientRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            ThreatWorkRepository threatWorkRepository,
            SignatureRepository signatureRepository,
            EvidenceFormWorkRepository evidenceFormWorkRepository,
            TaskDefinitionService taskDefinitionService) {
        return new SignWorkCommandHandler(objectMapper, serviceRecipientCommandRepository,
                clientRepository, supportWorkRepository, threatWorkRepository, evidenceFormWorkRepository,
                signatureRepository, taskDefinitionService);
    }

    @Bean
    public StatsController statsController(DataSource dataSource, QueuedCommandRepository queuedCmdRepository,
            UserRepository userRepository, EndpointStats endpointStats) {
        return new StatsController(dataSource, queuedCmdRepository, userRepository, endpointStats);
    }

    @Bean Log4j2LevelController log4j2LevelController() {
        return new Log4j2LevelController();
    }

    @Bean
    public ReportPublicController reportPublicController(SessionDataService sessionDataService,
                                                         ReportUnsecuredDelegator report,
                                                         EtaggedResponseCacheManager etaggedResponseCacheManager) {
        return new ReportPublicController(sessionDataService, report, etaggedResponseCacheManager);
    }

    @Bean
    public EvidenceAssociatedContactCommandHandler associatedContactCommandHandler(
            ObjectMapper objectMapper, ServiceRecipientCommandRepository serviceRecipientCommandRepository,
            EvidenceSupportWorkRepository supportWorkRepository,
            ClientRepository clientRepository,
            ContactRepository contactRepository,
            CalendarEventSnapshotRepository eventStatusRepository,
            ServiceRecipientRepository serviceRecipientRepository,
            ServiceRepository serviceRepository,
            ParentChildResolver parentChildResolver,
            EntityUriMapper entityUriMapper,
            CalendarService calendarService,
            TaskDefinitionService taskDefinitionService) {
        return new EvidenceAssociatedContactCommandHandler(objectMapper, serviceRecipientCommandRepository,
                supportWorkRepository, clientRepository, contactRepository, eventStatusRepository, serviceRecipientRepository,
                serviceRepository, parentChildResolver, entityUriMapper, calendarService, taskDefinitionService);
    }

    @Bean
    public TemplateController templateController(TemplateRepository templateRepository) {
        return new TemplateController(templateRepository);
    }

    @Bean
    public WorkerController workerController(
            HrService hrService, WorkerRepository workerRepository,
            WorkerJobRepository workerJobRepository,
            UserRepository userRepository,
            IdNameService idNameService,
            AddressRepository addressRepository,
            ListDefinitionRepository listDefinitionRepository,
            FixedContainerRepository fixedContainerRepository) {
        return new WorkerController(hrService, workerRepository, workerJobRepository, userRepository, idNameService, addressRepository,
                listDefinitionRepository, fixedContainerRepository);
    }

    @Bean
    WorkflowAdminController workflowAdminController(ActivitiWorkflowServiceImpl workflowService) {
        return new WorkflowAdminController(workflowService);
    }

    @Bean
    WorkflowController workflowController(ActivitiWorkflowServiceImpl workflowService,
                                          LinearWorkflowService linearWorkflowService,
                                          ServiceRecipientRepository serviceRecipientRepository,
                                          ServiceTypeService serviceTypeService,
                                          EntityUriMapper entityUriMapper) {
        return new WorkflowController(workflowService, linearWorkflowService, serviceRecipientRepository, serviceTypeService, entityUriMapper);
    }

    @Bean
    WorkflowDefinitionController workflowDefinitionController(ActivitiWorkflowServiceImpl workflowService) {
        return new WorkflowDefinitionController(workflowService);
    }

    @Bean
    WorkflowTaskController workflowTaskController(ActivitiWorkflowServiceImpl workflowService,
                                                  LinearWorkflowService linearWorkflowService,
                                                  EntityUriMapper entityUriMapper) {
        return new WorkflowTaskController(workflowService, linearWorkflowService, entityUriMapper);
    }

    @Bean
    TaskStatusController taskStatusController(TaskStatusRepository taskStatusRepository,
                                              ServiceRecipientRepository serviceRecipientRepository) {
        return new TaskStatusController(taskStatusRepository, serviceRecipientRepository);
    }

    @Bean
    WorkerJobController workerJobController(CreateServiceRecipientCommandController createServiceRecipientCommandController,
                                            ObjectMapper objectMapper,
                                            WorkerJobRepository workerJobRepository,
                                            ListDefinitionRepository listDefinitionRepository) {
        return new WorkerJobController(createServiceRecipientCommandController, objectMapper, workerJobRepository, listDefinitionRepository);
    }

    @Bean
    SecureCommandProxy secureCommandProxy(CommandQueueManagerService commandQueueManagerService) {
        return new SecureCommandProxy(commandQueueManagerService);
    }

    @Bean
    SecureQueryProxy secureQueryProxy(QueryManagerService queryManagerService) {
        return new SecureQueryProxy(queryManagerService);
    }

    @Bean
    public AclController aclController(@Qualifier("enableAcls") boolean enableAcls,
                                       @Qualifier("enableAclsConfig") boolean enableAclsConfig,
                                       AclHandler aclHandler, AclEntryRepository entryRepository,
                                       EntityRestrictionService entityRestrictionService,
                                       AclObjectIdentityRepository repository,
                                       UserManagementService userManagementService,
                                       ServicesService servicesService) {
        return new AclController(enableAcls, enableAclsConfig, aclHandler, entryRepository, entityRestrictionService,
                repository, userManagementService, servicesService);
    }


    @Bean
    public SettingsController settingsController(SettingsService settingsService) {
        return new SettingsController(settingsService);
    }

    @Bean
    public SchemaController schemaController() {
        return new SchemaController();
    }

    @Bean
    public ReferralListRowResourceAssembler referralListRowResourceAssembler(
            MessageSourceAccessor messageSource,
            ServiceTypeService serviceTypeService,
            ServiceCategorisationRepository serviceCategorisationRepository,
            IndividualRepository individualRepository
    ) {
        return new ReferralListRowResourceAssembler(messageSource, serviceTypeService, serviceCategorisationRepository, individualRepository);
    }

    @Bean
    public UserListRowResourceAssembler userListRowResourceAssembler() {
        return new UserListRowResourceAssembler();
    }

    @Bean
    public IncidentListRowResourceAssembler incidentListRowResourceAssembler(ListDefinitionRepository listDefinitionRepository,
                                                                             IndividualRepository individualRepository) {
        return new IncidentListRowResourceAssembler(listDefinitionRepository, individualRepository);
    }

    @Bean
    public WorkerListRowResourceAssembler workerListRowResourceAssembler() {
        return new WorkerListRowResourceAssembler();
    }

    @Bean
    public HttpTunnelSocket httpTunnelSocket(PathMatcher pathMatcher, MessageBus<ApplicationEvent> messageBus) {
        return new HttpTunnelSocket(pathMatcher, messageBus);
    }

    @Bean
    public TaskScheduler taskScheduler() {
        var scheduler = new ThreadPoolTaskScheduler();

        scheduler.setPoolSize(2);
        scheduler.setThreadNamePrefix("scheduled-task-");
        scheduler.setDaemon(true);

        return scheduler;
    }

//    @Bean
//    public UploadedFileController uploadedFileController(ApplicationProperties appProps,
//                                                         UploadConfigGenerator uploadConfigGenerator) {
//        return new UploadedFileController(appProps, uploadConfigGenerator);
//    }

}
