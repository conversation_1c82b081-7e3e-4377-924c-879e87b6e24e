package com.ecco.webApi.managedvoids;

import com.ecco.dom.managedvoids.ManagedVoid;
import com.ecco.serviceConfig.dom.ServiceCategorisation;

import javax.annotation.Nullable;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.function.Function;

public class ManagedVoidFromViewModel implements Function<ManagedVoidViewModel, ManagedVoid> {

    @PersistenceContext
    private EntityManager em;

    @Override
    @Nullable
    public ManagedVoid apply(@Nullable ManagedVoidViewModel input) {
        if (input == null) {
            throw new NullPointerException("input ManagedVoidViewModel must not be null");
        }

        ManagedVoid j = new ManagedVoid();
        j.setId(input.getManagedVoidId());
        j.setServiceTypeId(input.serviceTypeId);

        //j.setAddressHistoryId(input.getA());

        // FIXED service recipient allocation, but could come from the building OR type of fix
        var svcAllocId = ManagedVoid.DEFAULT_SERVICE_ALLOCATION_ID;
        var svcAlloc = em.getReference(ServiceCategorisation.class, svcAllocId);
        j.setServiceAllocation(svcAlloc);

        return j;
    }

}
