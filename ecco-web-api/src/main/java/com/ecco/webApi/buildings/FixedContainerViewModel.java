package com.ecco.webApi.buildings;


import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import org.jspecify.annotations.Nullable;
import org.springframework.hateoas.RepresentationModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// TODO extend ServiceRecipientSummary
public class FixedContainerViewModel extends RepresentationModel<FixedContainerViewModel> {

    public Integer buildingId;
    public String name;
    public Boolean disabled;
    public String externalRef;
    public Integer resourceTypeId;
    public String resourceTypeName;
    public Integer serviceRecipientId;
    public String calendarId;
    @Nullable
    public Integer parentId;
    @Nullable
    public String parentName;
    public Integer serviceAllocationId;
    public Integer locationId;
    public AddressViewModel address;
    @Nullable
    public List<ChargeCategoryCombination> chargeCategoryCombinations;
    public HashMap<String, String> textMap = new HashMap<>();
    public Map<String, ListDefinitionEntryViewModel> choicesMap = new HashMap<>();

    public static class ChargeCategoryCombination {
        @Nullable
        public Integer chargeCategoryId;
        @Nullable
        public Integer chargeNameId;

        public ChargeCategoryCombination() {}

        public ChargeCategoryCombination(@Nullable Integer chargeNameId, @Nullable Integer chargeCategoryId) {
            this.chargeNameId = chargeNameId;
            this.chargeCategoryId = chargeCategoryId;
        }
    }

}
