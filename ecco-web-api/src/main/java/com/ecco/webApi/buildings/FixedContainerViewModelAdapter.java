package com.ecco.webApi.buildings;

import com.ecco.buildings.viewModel.BuildingViewModel;
import com.ecco.webApi.contacts.address.AddressedLocationToViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.featureConfig.ListDefinitionEntryIdToViewModel;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import com.ecco.webApi.rota.AgreementController;
import com.ecco.webApi.rota.RotaController;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.ecco.infrastructure.rest.hateoas.ApiLinkTo.linkToApi;
import static org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn;

/**
 * Converts BuildingViewModel to FixedContainerViewModel with HATEOAS links.
 * Uses MapStruct for efficient compile-time mapping generation.
 */
@Mapper(componentModel = "spring", uses = {ListDefinitionEntryIdToViewModel.class})
public abstract class FixedContainerViewModelAdapter {

    protected final AddressedLocationToViewModel addressedLocationToViewModel = new AddressedLocationToViewModel();

    @Autowired
    protected ListDefinitionEntryIdToViewModel listDefinitionEntryIdToViewModel;

    @Mapping(source = "textMap", target = "textMap", qualifiedByName = "copyTextMap")
    @Mapping(source = "choicesMap", target = "choicesMap", qualifiedByName = "copyChoicesMap")
    @Mapping(source = "location", target = "address", qualifiedByName = "convertAddress")
    @Mapping(source = "chargeCategoryCombinations", target = "chargeCategoryCombinations", qualifiedByName = "mapChargeCombinations")
    public abstract FixedContainerViewModel toModel(BuildingViewModel input);

    @AfterMapping
    protected void addHateoasLinks(@MappingTarget FixedContainerViewModel result, BuildingViewModel input) throws IOException {
        if (input.buildingId != null) {
            // Self link
            result.add(linkToApi(methodOn(BuildingController.class).findOne(input.buildingId)).withSelfRel());

            // Parent link
            if (input.parentId != null) {
                result.add(linkToApi(methodOn(BuildingController.class).findOne(input.parentId)).withRel("parent"));
            }

            // Children/careruns link
            result.add(linkToApi(methodOn(BuildingController.class).findShifts(input.buildingId)).withRel("careruns"));

            // Rota links
            result.add(linkToApi(methodOn(RotaController.class).viewRota(null, null,"workers:all", "buildings:" + input.buildingId, true, true, null, null)).withRel("rota"));
            result.add(linkToApi(methodOn(AgreementController.class).listAgreementsByServiceRecipient(input.serviceRecipientId)).withRel("agreements"));
        }
    }

    @Named("copyTextMap")
    protected HashMap<String, String> copyTextMap(HashMap<String, String> textMap) {
        return textMap != null ? new HashMap<>(textMap) : new HashMap<>();
    }

    @Named("copyChoicesMap")
    protected Map<String, ListDefinitionEntryViewModel> copyChoicesMap(
            Map<String, Integer> choicesMap) {
        if (choicesMap == null) return new HashMap<>();

        Map<String, ListDefinitionEntryViewModel> result = new HashMap<>();
        for (Map.Entry<String, Integer> entry : choicesMap.entrySet()) {
            Integer choiceInfo = entry.getValue();
            if (choiceInfo != null) {
                // Use ListDefinitionEntryIdToViewModel to load from repository
                ListDefinitionEntryViewModel viewModel = listDefinitionEntryIdToViewModel.apply(choiceInfo);
                if (viewModel != null) {
                    result.put(entry.getKey(), viewModel);
                }
            }
        }
        return result;
    }

    @Named("convertAddress")
    protected AddressViewModel convertAddress(BuildingViewModel.AddressedLocationViewModel location) {
        if (location == null) return null;

        AddressViewModel result = new AddressViewModel();
        result.addressId = location.addressId;
        result.disabled = location.disabled;
        result.address = new String[]{location.line1, location.line2, location.line3};
        result.town = location.town;
        result.postcode = location.postcode;
        result.county = location.county;
        // Note: buildingName, buildingNumber, country are not part of AddressViewModel
        // disabled field would need to be added to AddressViewModel if required

        return result;
    }

    @Named("mapChargeCombinations")
    protected List<FixedContainerViewModel.ChargeCategoryCombination> mapChargeCombinations(
            List<BuildingViewModel.ChargeCategoryCombination> combinations) {
        if (combinations == null) return null;

        return combinations.stream()
                .map(combo -> {
                    FixedContainerViewModel.ChargeCategoryCombination result = new FixedContainerViewModel.ChargeCategoryCombination();
                    result.chargeCategoryId = combo.chargeCategoryId;
                    result.chargeNameId = combo.chargeNameId;
                    return result;
                })
                .toList();
    }

}
