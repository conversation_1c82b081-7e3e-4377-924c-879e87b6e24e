package com.ecco.config.dom;

import org.jspecify.annotations.Nullable;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Lob;
import javax.persistence.Table;

import com.ecco.infrastructure.entity.IdNameUsingIntegerKey;
import com.google.common.base.Function;
import com.google.common.base.Predicate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * A globally available reference list.  If we wish to hide values per service/service type, then that needs to be
 * a filter, such that these lists can be reported on across services.
 */
@Entity
@Table(name="cfg_list_definitions")
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@Data
public class ListDefinitionEntry extends IdNameUsingIntegerKey {

    /** Fixed pre-populated parent entry for buildings */
    public static int BUILDING_RESOURCETYPE_ID = 132;

    /** Fixed pre-populated parent entry for resources */
    public static int RESOURCE_RESOURCETYPE_ID = 133;

    public static int TASKSTATUS_COMPLETED_ID = 191;
    public static int TASKSTATUS_PARENTINACTIVE_ID = 192;

    private static final long serialVersionUID = 1L;

    public static final Predicate<ListDefinitionEntry> isDefaultPredicate = ListDefinitionEntry::isDefault;


    public static final Function<ListDefinitionEntry, String> extractListName = ListDefinitionEntry::getListName;

    @Column
    private String businessKey;

    @Column
    private boolean isDefault;

    @Column(name = "orderBy")
    private Integer order;

    @Column
    private String listName;

    @Nullable
    @Lob
    @Column(nullable = false)
    private String metadata;

    /**
     * @param parentId id that this list depends on
     * Optional
     */
    @Column
    @Nullable
    private Integer parentId;

    public void setMetadata(String metadata) {

        // Enforce not empty. If we are empty we get SessionData issues since the
        // JSON produced cannot be parsed.
        // Alternatives like hibernate @NotEmpty or use of @Pattern annotations are possible
        // see https://stackoverflow.com/a/43716689
        if (metadata != null && !metadata.startsWith("{\"")) {
            throw new RuntimeException("ListDefinitionEntry.metadata incorrect format");
        }

        this.metadata = metadata;
    }

}
