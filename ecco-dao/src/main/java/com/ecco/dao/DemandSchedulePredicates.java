package com.ecco.dao;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dom.agreements.DemandSchedule;
import com.ecco.dom.agreements.QServiceAgreement;
import com.ecco.infrastructure.entity.AbstractIntKeyedEntity;
import com.ecco.serviceConfig.repositories.ServiceCategorisationRepository;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.ecco.dom.QReferral.referral;
import static com.ecco.dom.agreements.QDemandSchedule.demandSchedule;
import static java.util.stream.Collectors.toList;

public abstract class DemandSchedulePredicates {

    // 132 is a list def 'resource type' for a bldg_fixed (the parent building)
    // 158 is a list def 'resource type' for a bldg_fixed (the care run)

    public static final int CARERUN_RESOURCE_ID = 158;
    public static final int CARERUN_AVAILABILITY_ID = 10;

    /** Return a predicate for where the schedule has any date that fall between the start and end date which
     * reduces to the startDate of the demand being before end, and the endDate of the demand being after start */
    public static BooleanExpression getWherePredicate(DateTime start, DateTime end,
            Class<? extends DemandSchedule> entityClass) {
        return demandSchedule.instanceOf(entityClass)
                .and(demandSchedule.start.loe(end.toLocalDate()))
                .and(demandSchedule.end.isNull()
                        .or(demandSchedule.end.goe(start.toLocalDate()))
                );
    }

    /** As getWherePredicate above, but for agreement of appointments */
    public static BooleanExpression getWherePredicateAgreements(LocalDate start, LocalDate end) {
        var agreement = QServiceAgreement.serviceAgreement;
        return agreement.start.loe(end)
                .and(agreement.end.isNull()
                    .or(agreement.end.goe(start))
                );
    }

    /** Get where predicate for from start of day at start, to end of day at end */
    public static BooleanExpression getWherePredicate(LocalDate start, LocalDate end,
            Class<? extends DemandSchedule> entityClass) {
        return getWherePredicate(start.toDateTimeAtStartOfDay(), end.plusDays(1).toDateTimeAtStartOfDay(), entityClass);
    }

    /** Get where predicate for one service recipient, for from start of day at start, to end of day at end */
    public static BooleanExpression getWherePredicateForDemandSchedulesOfServiceRecipient(DateTime start, DateTime end,
                                                                                          Class<? extends DemandSchedule> entityClass,
                                                                                          int serviceRecipientId) {
        return getWherePredicate(start, end, entityClass)
                .and(
                    demandSchedule.agreement.serviceRecipientId.in(Collections.singleton(serviceRecipientId))
                );
    }

    /** Filters to the demand schedules for the clients in the building and the building itself */
    public static BooleanExpression getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding(
            DateTime start, DateTime end, Class<? extends DemandSchedule> entityClass, int buildingId, int buildingServiceRecipientId,
            FixedContainerRepository fixedContainerRepository) {

        // get the bldg/units that clients may be assigned to with 'residenceId'
        // NB just one level deep
        // NB could possibly use findAllByLocationId?
        var buildingIds = fixedContainerRepository.findAllByParentId(buildingId)
                .stream().map(AbstractIntKeyedEntity::getId).collect(toList());
        buildingIds.add(buildingId);

        // get the DemandSchedule's for clients in the building
        JPQLQuery<Integer> insideBldgDemand = JPAExpressions.selectFrom(referral)
                .where(referral.client.residenceId.in(buildingIds))
                .select(referral.serviceRecipient.id);

        // get the DemandSchedule's for the building itself
        BooleanExpression directBldgDemand = demandSchedule.agreement.serviceRecipientId.eq(buildingServiceRecipientId);

        return getWherePredicate(start, end, entityClass)
            .and(
                demandSchedule.agreement.serviceRecipientId.in(insideBldgDemand)
                .or(directBldgDemand)
            );
    }

    /** Filters to the demand schedules for the clients in the building and the building itself */
    public static BooleanExpression getWherePredicateForDemandSchedulesOfCareRunsInBuilding(
            DateTime start, DateTime end, Class<? extends DemandSchedule> entityClass, int buildingId,
            FixedContainerRepository fixedContainerRepository) {

        var srIds = getSrIdsForDemandSchedulesOfCareRunsInBuilding(buildingId, fixedContainerRepository);
        return getWherePredicate(start, end, entityClass)
                .and(demandSchedule.agreement.serviceRecipientId.in(srIds));
    }
    public static Collection<Integer> getSrIdsForDemandSchedulesOfCareRunsInBuilding(
            int buildingId, FixedContainerRepository fixedContainerRepository) {

        // TODO a quicker load would be good but 'id' isn't available for JPAExpressions.selectFrom()
        // TODO also we assume that care runs only have availability on their agreements - probably should filter apts with CARERUN_AVAILABILITY_ID
        var ids = Collections.singletonList(buildingId);
        List<FixedContainer> careRuns = fixedContainerRepository.findAllByParentIdInAndResourceTypeIdAndDisabledFalse(ids, CARERUN_RESOURCE_ID);

        return careRuns.stream().map(FixedContainer::getServiceRecipientId).collect(toList());
    }

    public static BooleanExpression getWherePredicateForDemandSchedulesOfClientsInServiceCategorisations(
            DateTime start, DateTime end, Class<? extends DemandSchedule> entityClass, List<Integer> svcCatIds) {

        // get the DemandSchedule's for clients in the service categorisation
        JPQLQuery<Integer> insideServiceDemand = JPAExpressions.selectFrom(referral)
                .where(referral.serviceRecipient.serviceAllocation.id.in(svcCatIds))
                .select(referral.serviceRecipient.id);

        return getWherePredicate(start, end, entityClass)
                .and(demandSchedule.agreement.serviceRecipientId.in(insideServiceDemand));
    }

    /** Filters to the demand schedules for the clients in the service categorisation */
    public static BooleanExpression getWherePredicateForDemandSchedulesOfCareRunsInServiceCategorisations(
            DateTime start, DateTime end, Class<? extends DemandSchedule> entityClass, List<Integer> svcCatIds,
            ServiceCategorisationRepository serviceCategorisationRepository, FixedContainerRepository fixedContainerRepository) {

        var careRunSrIds = svcCatIds.stream()
                .map(scId -> serviceCategorisationRepository.findById(scId).orElseThrow().getBuildingId())
                .filter(Objects::nonNull)
                .map(bldgId -> getSrIdsForDemandSchedulesOfCareRunsInBuilding(bldgId, fixedContainerRepository))
                .flatMap(Collection::stream).collect(toList());

        return getWherePredicate(start, end, entityClass)
                .and(demandSchedule.agreement.serviceRecipientId.in(careRunSrIds));
    }

}
