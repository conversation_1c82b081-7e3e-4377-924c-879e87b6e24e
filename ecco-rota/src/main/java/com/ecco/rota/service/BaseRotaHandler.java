package com.ecco.rota.service;

import com.ecco.calendar.CombinedEntry;
import com.ecco.dao.DemandSchedulePredicates;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dao.ServiceAgreementRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.QDemandSchedule;
import com.ecco.dom.agreements.QServiceAgreement;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.rota.webApi.dto.AppointmentCollection;
import com.ecco.rota.webApi.dto.RotaAppointmentViewModel;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.ecco.service.EventService;
import com.ecco.calendar.core.Recurrence;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import org.joda.time.Interval;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.Collection;
import java.util.EnumSet;
import java.util.List;

import static java.util.Collections.singletonList;

public abstract class BaseRotaHandler implements RotaHandler {

    public enum ActivityState {
        CONFIRMED, UNCONFIRMED, DROPPED
    }

    protected final Logger log = LoggerFactory.getLogger(getClass());

    protected ServiceAgreementRepository agreementsRepository;
    protected RotaService rotaService;
    protected EventService eventService;

    protected final DemandScheduleRepository demandScheduleRepository;

    @PersistenceContext
    protected EntityManager em;

    /** Utility function to create using EntityManager but match {@link JPAExpressions#selectFrom(EntityPath)} which
     * is only for detached queries (e.g. subqueries).
     */
    protected <T> JPAQuery<T> query(EntityPath<T> entityPath) {
        return new JPAQuery<T>(em)
                .from(entityPath);
//                .select(entityPath); // originally just .from and our select can change according to projection
    }

    public BaseRotaHandler(RotaService rotaService,
                           DemandScheduleRepository demandScheduleRepository,
                           ServiceAgreementRepository agreementsRepository,
                           EventService eventService) {
        this.demandScheduleRepository = demandScheduleRepository;
        this.rotaService = rotaService;
        this.agreementsRepository = agreementsRepository;
        this.eventService = eventService;
    }

    /** NOTE: gets ALL agreements - it will need filtering down */
    @Override
    public List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params) {
        BooleanExpression wherePredicate = DemandSchedulePredicates.getWherePredicate(params.getStartDate(), params.getEndDate(), AppointmentSchedule.class);

        JPQLQuery<Integer> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate)
                .select(QDemandSchedule.demandSchedule.agreement.serviceRecipientId)
                .distinct();

        return query.fetch();
    }

    /** NOTE: gets ALL agreements - it will need filtering down */
    @Override
    public List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params) {
        BooleanExpression wherePredicate = DemandSchedulePredicates.getWherePredicateAgreements(params.getStartDate(), params.getEndDate());

        JPQLQuery<ServiceAgreement> query = query(QServiceAgreement.serviceAgreement)
                .where(wherePredicate)
                .distinct();

        return query.fetch();
    }

    abstract protected BooleanExpression getDemandScheduleWherePredicate(RotaParams rota);

    /**
     * Returns the specificId from type:all|specificId (:specificId is designed for caching)
     */
    public static Integer getResourceServiceRecipientId(String resourceFilter) {
        if (resourceFilter == null) {
            return null;
        }
        String[] split = resourceFilter.split(":");
        if (split.length == 1) {
            return null;
        }
        return split[1].equalsIgnoreCase("all")
                ? null
                : Integer.parseInt(split[1]);
    }

    /**
     * Returns specificId from buildings:bldgId:srId
     */
    public static Integer getDemandServiceRecipientId(String demandFilter) {
        if (demandFilter == null) {
            return null;
        }
        String[] split = demandFilter.split(":");
        return split.length == 2 ? null : Integer.parseInt(split[2]);
    }

    public void addDemandWithStatuses(RotaParams params, AppointmentCollection rota, EnumSet<Recurrence.Status> recurrenceStatuses,
                                      BooleanExpression wherePredicate) {
        rotaService.addDemandWithStatuses(params, rota, recurrenceStatuses, wherePredicate);
    }

    /**
     * Include events on the rota, so we can get Holiday and Sick leave etc.
     * We deem anything with an event category worthy of being on the rota (as per our 'project calendar').
     * However, it appears that the 'project calendar' doesn't restrict to things with a category, as it
     * uses EventService.getCalendars (see calendarRepository.fetchCalendar) which means that is just getting
     * all events and filtering to a 'no category' (see calendarAnalaysis.groupByCalendarCategory).
     * Using EventService.getCalendars would also bring back rota events, so we filter out.
     */
    protected void addEventsWithCategory(Interval interval, RotaResourceViewModel rotaEntry, String calendarId) {
        Collection<CombinedEntry> allEntries = eventService.getCalendars(singletonList(calendarId), interval.getStart(), interval.getEnd());
        allEntries.stream()
                .filter(e -> e.getEventEntry() != null && e.getEventEntry().getEventCategory() != null)
                // probably need to split for each day of the interval in week view
                .map(e -> new RotaAppointmentViewModel(e.getIcalEntry()))
                .forEach(rotaEntry::addAppointment);
    }
}
