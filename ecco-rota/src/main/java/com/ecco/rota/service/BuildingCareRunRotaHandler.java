package com.ecco.rota.service;

import com.ecco.buildings.dom.BuildingServiceRecipient;
import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.repositories.FixedContainerRepository;
import com.ecco.dao.DemandScheduleRepository;
import com.ecco.dom.agreements.AppointmentSchedule;
import com.ecco.dom.agreements.QDemandSchedule;
import com.ecco.dom.agreements.ServiceAgreement;
import com.ecco.rota.webApi.dto.Rota;
import com.ecco.rota.webApi.dto.RotaParams;
import com.ecco.rota.webApi.dto.RotaResourceViewModel;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Interval;
import org.jspecify.annotations.NonNull;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ecco.calendar.core.Recurrence.Status.DROPPED;
import static com.ecco.calendar.core.Recurrence.Status.TENTATIVE;

/**
 * Handles care run resource with building demand (inc their referrals) - which is used on the run builder page
 * This is the run builder.
 * See BuildingWorkerRotaHandler for the live rota.
 */
@Slf4j
public class BuildingCareRunRotaHandler implements RotaHandler {

    private static final String DEMAND_HANDLER_PREFIX = "buildings:"; // demand is the building which includes its demands and its associated referrals demands
    private static final String RESOURCE_HANDLER_PREFIX = "careruns:"; // resource is the careruns (down the left, on the run builder)
    public static final int CARERUN_RESOURCE_ID = 158;

    private final FixedContainerRepository fixedContainerRepository;
    private final BuildingWorkerRotaHandler buildingWorkerRotaHandler;
    private final DemandScheduleRepository demandScheduleRepository;

    protected final RotaService rotaService;

    @PersistenceContext
    protected EntityManager em;

    /** Utility function to create using EntityManager but match {@link JPAExpressions#selectFrom(EntityPath)} which
     * is only for detached queries (e.g. subqueries).
     */
    protected <T> JPAQuery<T> query(EntityPath<T> entityPath) {
        return new JPAQuery<T>(em)
                .from(entityPath);
//                .select(entityPath); // originally just .from and our select can change according to projection
    }

    public BuildingCareRunRotaHandler(@NonNull final FixedContainerRepository fixedContainerRepository,
                                      @NonNull final BuildingWorkerRotaHandler buildingWorkerRotaHandler,
                                      @NonNull final DemandScheduleRepository demandScheduleRepository,
                                      @NonNull final RotaService rotaService) {
        this.fixedContainerRepository = fixedContainerRepository;
        this.buildingWorkerRotaHandler = buildingWorkerRotaHandler;
        this.demandScheduleRepository = demandScheduleRepository;
        this.rotaService = rotaService;
    }

    @Override
    public boolean canHandle(String resourceFilter, String demandFilter) {
        return resourceFilter.startsWith(RESOURCE_HANDLER_PREFIX) && demandFilter.startsWith(DEMAND_HANDLER_PREFIX);
    }

    @Override
    public List<Integer> findAllResourceServiceRecipientIds(RotaParams params) {
        return this.findRelevantResources(params)
            .stream().map(f -> f.getServiceRecipientId())
            .collect(Collectors.toList());
    }

    @Override
    public void populateRota(Rota rota) {
        // add resources careruns
        // this loads the carerun's calendar and adds CONFIRMED
        if (rota.getLoadResource()) {
            addCareRunResources(rota);
        }

        // EXACTLY as per BuildingWorkerRotaHandler, but allows ServiceCatCareRunRotaHandler to override getDemandScheduleWherePredicate
        if (rota.getLoadDemand()) {
            rotaService.addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.buildingWorkerRotaHandler.getDemandScheduleWherePredicate(rota));
            // NB this was refactored in BuildingWorkerRotaHandler, and copied here in ec027871 (part of ServiceCat rota handlers)
            // seemingly it was missing this clause, which 'load the unallocated carerun demand (see a362461e)' - avoiding runs being loaded each time
            // meaning that the above loads getWherePredicateForDemandSchedulesOfClientsInBuildingAndBuilding for the referral/bldg demand
            // and this loads getWherePredicateForDemandSchedulesOfCareRunsInBuilding for the care runs demand in the building
            if (BaseRotaHandler.getDemandServiceRecipientId(rota.getDemandFilter()) == null) {
                rotaService.addDemandWithStatuses(rota, rota, EnumSet.of(TENTATIVE, DROPPED), this.buildingWorkerRotaHandler.getDemandScheduleCareRunsWherePredicate(rota));
            }
        }
    }

    @Override
    public String getResourceCalendarId(int resourceId) {
        FixedContainer careRun = this.fixedContainerRepository.findOne(resourceId);
        return careRun.getCalendarId();
    }

    @Override
    public List<Integer> findAllAgreementSrIdsByScheduleDate(RotaParams params) { // Takes 1-3 sec on Ryzen 2600
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        JPQLQuery<Integer> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate)
                .select(QDemandSchedule.demandSchedule.agreement.serviceRecipientId)
                .distinct();
        return query.fetch();
    }

    @Override
    public List<ServiceAgreement> findAllAgreementsByScheduleDate(RotaParams params) { // Takes 1-3 sec on Ryzen 2600
        // we only use this to get the srIds for the rota page
        // indeed most 'findAllAgreements' are not really wanting the agreements
        BooleanExpression wherePredicate = getDemandScheduleWherePredicate(params);
        JPQLQuery<ServiceAgreement> query = query(QDemandSchedule.demandSchedule)
                .where(wherePredicate)
                .select(QDemandSchedule.demandSchedule.agreement)
                .distinct();
        return query.fetch();
    }

    protected BooleanExpression getDemandScheduleWherePredicate(RotaParams params) {
        return buildingWorkerRotaHandler.getDemandScheduleWherePredicate(params);
    }

    private int getResourceCareRunId(RotaParams rota) {
        return getCareRunServiceRecipientId(rota.getResourceFilter());
    }

    private boolean isCareRunsAll(String resourceFilter) {
        var specificResourceSrId = BaseRotaHandler.getResourceServiceRecipientId(resourceFilter);
        return specificResourceSrId == null;
    }

    private int getCareRunServiceRecipientId(String resourceFilter) {
        Objects.requireNonNull(resourceFilter);
        String[] split = resourceFilter.split(":");
        return Integer.parseInt(split[1]);
    }

    /**
     * Finds the care runs for the resource filter - be it one or all
     */
    protected List<FixedContainer> findRelevantResources(RotaParams params) {
        //addWorkersEntries(rota, workers, ALLOCATED_APPOINTMENTS, AVAILABILITY, ALL_DAY_EVENTS);
        List<FixedContainer> careRuns = isCareRunsAll(params.getResourceFilter())
                ? fixedContainerRepository.findAllByParentIdInAndResourceTypeIdAndDisabledFalse(getBuildingIds(params), CARERUN_RESOURCE_ID)
                : List.of(fixedContainerRepository.findByServiceRecipient_Id(getResourceCareRunId(params)).orElseThrow());
        return careRuns;
    }

    protected void addCareRunResources(final Rota rota) {

        var careRuns = this.findRelevantResources(rota);

        DateTime start = rota.getStartDate().toDateTimeAtStartOfDay();
        DateTime end = rota.getEndDate().plusDays(1).toDateTimeAtStartOfDay();
        for (FixedContainer careRun : careRuns) {
            final String calendarId = careRun.getCalendarId();

            // TODO we should allow another filter for scheduleId because careruns:srId can bring back a lot
            //      and possibly from the ui also - the ability to see just one s-id in resource and/or demand
            // DEBUG - filter a specific s-id
            //var demandSchedules = StreamSupport.stream(demandSchedulesAll.spliterator(), false)
            //        .filter(ds -> ds.getId().equals(107152L)).collect(Collectors.toList());

            if (calendarId != null) {
                RotaResourceViewModel rotaEntry = createOrFindRotaEntry(rota, careRun);
                rotaEntry.setResourceId(careRun.getId().longValue());
                // TODO what is the calendarId for client-side?
                rotaEntry.setCalendarId(calendarId);
                populateCareRunEntry(start, end, careRun, rotaEntry, calendarId);
            }
        }
    }

    protected List<Integer> getBuildingIds(RotaParams rota) {
        return Collections.singletonList(buildingWorkerRotaHandler.getDemandBuilding(rota).getId());
    }

    private RotaResourceViewModel createOrFindRotaEntry(Rota rota, FixedContainer careRun) {
        return rota.createOrFindResourceEntry(careRun.getDisplayName(), careRun.getServiceRecipientId(), BuildingServiceRecipient.DISCRIMINATOR);
    }

    private void populateCareRunEntry(DateTime start, DateTime end, FixedContainer careRun, RotaResourceViewModel rotaEntry, String calendarId) {

        // Care runs have a demandSchedule for availability
        //      rotaService.findRecurrences(handle, range, null).forEach(recurrence -> {
        // NB the care run demandSchedule can be part-allocated onto staff, meaning we need the to get the availability from the series

        // Care runs have a calendarId which can be looked at to see confirmed appointment attendees (clients) on the run
        // NB although we want to use the pattern availability 'findRecurrences' to get availability and allocated appointments, the availability
        // duration is returned and not the client apt duration - so we need to use the calendarId of the schedule and load that
        //      rotaService.findRecurrencesFromCalendar(calendarId, new Interval(start, end)).forEach(recurrence -> {
        //      Alternatively, we would need to load appointments by loading each srId to determine those assigned to the run (very costly).
        // NB the care run calendarId can be part-allocated, meaning we need the to get the attendees from the schedule's calendarId
        // NB the care run isn't visible in the ui, so we don't get pages that show partial events on the calendarId

        // NB we can load the calendar to get both demand and appointments - the careRun.getServiceRecipientId can be
        // used to determine whether the appointment is an availability or appointment.

        var rotaInterval = new Interval(start, end);
        rotaService.findRecurrencesFromCalendar(calendarId, rotaInterval).forEach(recurrence ->
            rotaService.addRelevantRecurrences(recurrence, rotaEntry, AppointmentSchedule.class, careRun.getServiceRecipientId())
        );
        /*
        var schedules = DemandSchedulePredicates.getWherePredicateForDemandSchedulesOfServiceRecipient(start, end, AppointmentSchedule.class, careRun.getServiceRecipientId());
        var rotaInterval = new Interval(start, end);
        // loop for all the careruns schedules
        // NB because we allocate to the carerun's calendarId and not each availability schedule calendarId
        // this looping just means we get the same appointments back, repeated
        demandScheduleRepository.findAll(schedules).forEach(ds -> {
            // NB the range is the rota start/end, and not restricted also to the schedule start/end - so it is possible a rogue schedule could 'leak' data to the rota
            // and, with historical repeated single allocations stretching into the future (on the one carerun calendarId) we do in fact need to restrict this date range
            var calendarIdForSchedule = rotaService.findCalendarIdFromItemId(ds.getRecurringEntryHandle().toString());
            // intercepted to get all the schedule calendarId's in the range
            rotaService.findRecurrencesFromCalendar(calendarIdForSchedule, rotaInterval).forEach(recurrence ->
                rotaService.addRelevantRecurrences(recurrence, rotaEntry, AppointmentSchedule.class, careRun.getServiceRecipientId())
            );
        });*/
    }

}
