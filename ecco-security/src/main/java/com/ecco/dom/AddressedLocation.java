package com.ecco.dom;

import static com.ecco.infrastructure.util.EccoStringUtils.appendIfExists;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;

import com.ecco.infrastructure.entity.AbstractIntKeyedEntityWithUuid;
import com.ecco.dom.contacts.AddressLike;

/**
 * A unique address should always refer to the same thing, and only be able to be
 * edited if for example the postcode changes. This would be done via an admin screen
 * where there was no chance of a user relating it to a person or company.
 *
 * Unique index required on postcode,line1 for lookup.
 *
 * Ultimately, the options for line1 should match the results given when putting in
 * a postcode at http://www.postoffice.co.uk/postcode-finder
 *
 */
@Entity
@Table(name = "bldg_addresses")
@Getter
@Setter
public class AddressedLocation extends AbstractIntKeyedEntityWithUuid implements AddressLike {

    private static final long serialVersionUID = 1L;

    @Column
    private String buildingName;

    @Column
    private String buildingNumber;

    @Column
    private String line1;

    @Column
    private String line2;

    @Column
    private String line3;

    @Column
    private String town;

    @Column
    private String county;

    @Column
    private String postCode;

    @Column
    private String country;

    @Column
    private boolean disabled;

    public void setPostCode(String postCode) {
        this.postCode = AddressLike.formatPostcode(postCode);
    }

    /* (non-Javadoc)
     * @see com.ecco.buildings.dom.AddressLike#toCommaSepString()
     */
    @Override
    public String toCommaSepString() {
        final String separator = ", ";
        StringBuilder builder = new StringBuilder()
            .append(appendIfExists(getBuildingName(), separator))
            .append(appendIfExists(getBuildingNumber(), separator))
            .append(appendIfExists(getLine1(), separator))
            .append(appendIfExists(getLine2(), separator))
            .append(appendIfExists(getLine3(), separator))
            .append(appendIfExists(getTown(), separator))
            .append(appendIfExists(getCounty(), separator))
            .append(appendIfExists(getPostCode(), separator))
            .append(appendIfExists(getCountry(), separator));

        // lose the last separator and space
        String commandStr = builder.toString();
        if (commandStr.length() > 2) {
            commandStr = commandStr.substring(0, commandStr.length() - 2);
        }
        return commandStr;
    }

    @Override
    public String toString() {
        return toCommaSepString();
    }

    public AddressedLocation withStreetAddress(String... lines) {
        Assert.state(lines.length > 0 && lines.length <= 3, "between 1 and 3 lines must be specified");
        this.line1 = lines[0];
        if (lines.length > 1) {
            this.line2 = lines[1];
        }
        if (lines.length > 2) {
            this.line3 = lines[2];
        }
        return this;
    }

    public AddressedLocation withBuildingName(String name) {
        this.buildingName = name;
        return this;
    }

    public AddressLike withBuildingNumber(String number) {
        this.buildingNumber = number;
        return this;
    }

    public AddressedLocation withTown(String town) {
        this.town = town;
        return this;
    }

    public AddressLike withCounty(String county) {
        this.county = county;
        return this;
    }

    public AddressLike withCountry(String country) {
        this.country = country;
        return this;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = super.hashCode();
        result = prime * result + ((buildingName == null) ? 0 : buildingName.hashCode());
        result = prime * result + ((buildingNumber == null) ? 0 : buildingNumber.hashCode());
        result = prime * result + ((country == null) ? 0 : country.hashCode());
        result = prime * result + ((county == null) ? 0 : county.hashCode());
        result = prime * result + ((line1 == null) ? 0 : line1.hashCode());
        result = prime * result + ((line2 == null) ? 0 : line2.hashCode());
        result = prime * result + ((line3 == null) ? 0 : line3.hashCode());
        result = prime * result + ((postCode == null) ? 0 : postCode.hashCode());
        result = prime * result + ((town == null) ? 0 : town.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!super.equals(obj)) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        AddressedLocation other = (AddressedLocation) obj;
        if (buildingName == null) {
            if (other.buildingName != null) {
                return false;
            }
        } else if (!buildingName.equals(other.buildingName)) {
            return false;
        }
        if (buildingNumber == null) {
            if (other.buildingNumber != null) {
                return false;
            }
        } else if (!buildingNumber.equals(other.buildingNumber)) {
            return false;
        }
        if (country == null) {
            if (other.country != null) {
                return false;
            }
        } else if (!country.equals(other.country)) {
            return false;
        }
        if (county == null) {
            if (other.county != null) {
                return false;
            }
        } else if (!county.equals(other.county)) {
            return false;
        }
        if (line1 == null) {
            if (other.line1 != null) {
                return false;
            }
        } else if (!line1.equals(other.line1)) {
            return false;
        }
        if (line2 == null) {
            if (other.line2 != null) {
                return false;
            }
        } else if (!line2.equals(other.line2)) {
            return false;
        }
        if (line3 == null) {
            if (other.line3 != null) {
                return false;
            }
        } else if (!line3.equals(other.line3)) {
            return false;
        }
        if (postCode == null) {
            if (other.postCode != null) {
                return false;
            }
        } else if (!postCode.equals(other.postCode)) {
            return false;
        }
        if (town == null) {
            if (other.town != null) {
                return false;
            }
        } else if (!town.equals(other.town)) {
            return false;
        }
        return true;
    }

}
