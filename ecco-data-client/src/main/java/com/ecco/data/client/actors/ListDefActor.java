package com.ecco.data.client.actors;

import com.ecco.data.client.WebApiSettings;
import com.ecco.webApi.featureConfig.ListDefCommandHandler;
import com.ecco.webApi.listsConfig.ListDefinitionEntryViewModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.Collection;
import java.util.Map;

/**
 * API actions relating to group support.
 */
@Slf4j
public class ListDefActor extends BaseActor {

    private static final String listDefUri = WebApiSettings.APPLICATION_URL + "/api/listdefinitions/";

    public ListDefActor(RestTemplate restTemplate) {
        super(restTemplate);
    }

    public HttpEntity<Map<String, Collection<ListDefinitionEntryViewModel>>> getAllListDefs() {
        ParameterizedTypeReference<Map<String, Collection<ListDefinitionEntryViewModel>>> typeRef =
                new ParameterizedTypeReference<>() {};
        return restTemplate.exchange(listDefUri, HttpMethod.GET, null, typeRef);
    }

    public Map<String, Collection<ListDefinitionEntryViewModel>> createListDefinitionEntry(ListDefinitionEntryViewModel vm, boolean ignoreAlreadyExists) {
        // whilst we still have imports using direct creations, we don't use commands
        //var cmd = new ListDefCommandViewModel(ListDefCommandViewModel.OPERATION_ADD);
        createListDefinitionEntryResponse(vm, ignoreAlreadyExists);
        Map<String, Collection<ListDefinitionEntryViewModel>> allLists = getAllListDefs().getBody();
        log.info("allLists count: {}", allLists.size());
        return allLists;
    }

    public Collection<ListDefinitionEntryViewModel> ensureAndReturnListDefinitionEntry(String listName, ListDefinitionEntryViewModel... vms) {
        for (ListDefinitionEntryViewModel vm : vms) {
            createListDefinitionEntryResponse(vm, true);
        }
        Map<String, Collection<ListDefinitionEntryViewModel>> allLists = this.getAllListDefs().getBody();
        return allLists.get(listName);
    }

    private void createListDefinitionEntryResponse(ListDefinitionEntryViewModel vm, boolean ignoreAlreadyExists) {
        try {
            if (vm.businessKey == null) {
                vm.businessKey = ListDefCommandHandler.generateBusinessKey(vm.listName, vm.name);
            }
            postAsJson(listDefUri + vm.listName + "/?appendable=true", vm);
        } catch (HttpClientErrorException e) {
            // continue - it probably exists
            if (ignoreAlreadyExists) {
                log.info(e.getResponseBodyAsString());
                //if (result.getStatusCode().is4xxClientError()) {
                //    assertTrue(StringUtils.contains(result.getBody().getMessage(), "entry already exists in the database"));
                //}
            } else {
                throw e;
            }
        }
    }

}
