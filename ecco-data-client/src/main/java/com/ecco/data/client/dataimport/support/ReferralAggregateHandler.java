package com.ecco.data.client.dataimport.support;

import static org.apache.commons.lang3.StringUtils.containsIgnoreCase;
import static org.springframework.util.StringUtils.hasText;

import com.ecco.data.client.model.ReferralAggregate;
import com.ecco.dom.ReferralStatusName;
import com.ecco.dom.ReportCriteriaDto;
import com.ecco.webApi.contacts.ClientViewModel;
import com.ecco.webApi.contacts.IndividualViewModel;
import com.ecco.webApi.evidence.ReferralViewModel;
import com.ecco.webApi.listsConfig.*;

import com.ecco.webApi.viewModels.Result;
import com.ecco.webApi.viewModels.ServicesViewModel;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.Map.Entry;

/**
 * Handles the import of a spreadsheet row which typically includes referral and client information.
 * Also allows extra processing before populating the ReferralViewModel and ClientViewModel.
 */
public class ReferralAggregateHandler extends AbstractHandler<ReferralAggregate> {

    /**
     * Processes a ReferralAggregate assuming all the reference data used is valid data.
     * The original usage of this class assumed no reference data was valid (separate import)
     * so we introduce a flag which can be changed
     * @see ReferralAggregateImporter where this variable is set
     */
    boolean strictReferenceData = false;

    /**
     * Switch the submission from POST to PUT, to indicate we want to update things.
     * Only specify the csv file only with the properties wanting to update (to avoid incorrect updates and extra processing)
     * (NB with the approach of processEntity only looking for non-empty, we can't easily set something to empty/null)
     *
     * Currently, other entities retain the postAcceptingCreatedOrUnprocessableEntity which returns the id if the code already exists.
     * Currently, only the REFERRAL is updated, AND changes are also restricted to what is allowed in ReferralController.update.
     */
    final Boolean updateMode;

    public Map<String, Collection<ListDefinitionEntryViewModel>> listDefEntries;
    public ServicesViewModel servicesWithProjects;

    public ReferralAggregateHandler(RestTemplate restTemplate) {
        super(restTemplate);
        this.updateMode = false;
    }
    public ReferralAggregateHandler(RestTemplate restTemplate, boolean strictReferenceData, boolean updateMode) {
        super(restTemplate);
        this.strictReferenceData = strictReferenceData;
        this.updateMode = updateMode;
    }

    Map<Integer, List<ReferralViewModel>> referralsByService = new HashMap<>();
    private List<ReferralViewModel> findReferralsOnService(long serviceId) {
        ReportCriteriaDto dto = new ReportCriteriaDto();
        dto.setReferralStatus(ReferralStatusName.Ongoing.getName());
        dto.setServiceId(serviceId);
        ResponseEntity<ReferralViewModel[]> responseReport = this.reportActor.getAllReportReferrals(dto);
        return Arrays.stream(responseReport.getBody()).toList();
    }

    private int getServiceIdFromName(String name) {
        return this.servicesWithProjects.services
                .stream()
                .filter(s -> s.getName().equalsIgnoreCase(name))
                .findFirst()
                .get()
                .id;
    }

    @Override
    protected void processEntity(ImportOperation<ReferralAggregate> operation) {
        ReferralAggregate input = operation.record;

        if (this.listDefEntries == null) {
            var lists = listDefActor.getAllListDefs();
            this.listDefEntries = lists != null ? lists.getBody() : null;
        }

        if (this.servicesWithProjects == null) {
            var services = serviceActor.getAllServicesWithProjects();
            this.servicesWithProjects = services != null ? services.getBody() : null;
        }

        // check the incoming serviceName starts with an existing service name
        // because it seems we get given service names with extra detail in brackets
        boolean partialMatchServiceName = false;
        if (partialMatchServiceName) {
            var serviceMatches = this.servicesWithProjects.services.stream().filter(s -> input.referral.importServiceName.startsWith(s.getName())).toList();
            if (serviceMatches.size() != 1) {
                throw new IllegalArgumentException("no service name match");
            } else {
                input.referral.importServiceName = serviceMatches.get(0).name;
            }
        }

        if (updateMode && input.referral.referralCode == null && input.referral.importServiceName != null) {
            int sId = getServiceIdFromName(input.referral.importServiceName);
            if (this.referralsByService.get(sId) == null) {
                this.referralsByService.put(sId, findReferralsOnService(sId));
            }
        }

        // lookups
        if (StringUtils.hasText(input.ethnicOriginLookup)) {
            var item = lookupOrCreateListDef("ethnicOrigin", input.ethnicOriginLookup);
            input.client.ethnicOriginId = item.id;
        }
        if (StringUtils.hasText(input.genderLookup)) {
            var item = lookupOrCreateListDef("gender", input.genderLookup);
            input.client.genderId = item.id;
        }
        if (StringUtils.hasText(input.sexualOrientationLookup)) {
            var item = lookupOrCreateListDef("sexualOrientation", input.sexualOrientationLookup);
            input.client.sexualOrientationId = item.id;
        }
        if (StringUtils.hasText(input.religionLookup)) {
            var item = lookupOrCreateListDef("religion", input.religionLookup);
            input.client.religionId = item.id;
        }
        if (StringUtils.hasText(input.srcGeographicAreaLookup)) {
            var item = lookupOrCreateListDef("country-list", input.srcGeographicAreaLookup);
            input.referral.srcGeographicAreaId = item.id;
        }

        // HACK for NHS - we should have .code supplied (or could derive it and do server side collision check)
        input.deliveredBy.code = input.deliveredBy.companyName;
        if (hasText(input.deliveredBy.code)) {
            //input.deliveredBy.agencyCategoryId = "partners"; // fixed list for use in deliveredBy
            input.referral.deliveredById = syncAgencyToServer(operation.baseUri, input.deliveredBy);
        }

        // HACK for RT/ML - special processing of 'source' referrer
        // their source of referral is free text - so could include 'self' as text, or the name of the person or organisation
        // in fact very few cases marked 'self' actually mean self - they mean individual
        // so we actually just want to exclude a company name called 'Self referral - non professional'
        if ((input.referrerIndividual != null) && (input.referrerIndividual.organisation != null)) {
            if (containsIgnoreCase(input.referrerIndividual.organisation.companyName, "self referral")) {
                input.referrerIndividual.organisation = null;
            }
        }
        // we also can use a spel column to determine if its a self-referral
        if (input.selfReferral) {
            input.referral.source = ReferralViewModel.sourceAsSelfReferral;
        }

        // referral 'contacts'
        // referring agent/individual
        if (input.referrerIndividual != null) {
            if (hasText(input.referrerIndividual.firstName) || hasText(input.referrerIndividual.lastName)) {
                input.referral.referrerIndividualId = syncIndividualToServer(operation.baseUri, input.referrerIndividual, strictReferenceData);
                // if there was an agency, put it on the referral for later saving
                input.referral.referrerAgencyId = input.referrerIndividual.organisationId;
                // sync agency even if we don't have an individual in it
            } else if (input.referrerIndividual.organisation != null) {
                input.referral.referrerAgencyId = syncAgencyToServer(operation.baseUri, input.referrerIndividual.organisation);
            }
        }

        for (Entry<String, IndividualViewModel> entry : input.referralIndividuals.entrySet()) {
            // currently the ui requires a first name/last name on the contacts, so we ensure one of them is set
            // if not, we just assume its a blank record because thats what it often is
            if (hasText(entry.getValue().firstName) || hasText(entry.getValue().lastName)) {
                input.referral.individualContacts.add(syncIndividualToServer(operation.baseUri, entry.getValue(), strictReferenceData));
            }
        }

        // referral choices = choiceMap because we can't set the property directly
        // on the ReferralViewModel through bean mapping (the array lookup isn't itself a property)
        for (Entry<String, ListDefinitionEntryViewModel> entry : input.choices.entrySet()) {
            // currently the ui requires a first name/last name on the contacts, so we ensure one of them is set
            // if not, we just assume its a blank record because thats what it often is
            if (null != entry.getValue().id) {
                ListDefinitionEntryViewModel cvm = new ListDefinitionEntryViewModel();
                cvm.id = entry.getValue().id;
                // NB the name is not used in ReferralFromViewModel
                //cvm.name = entry.getValue().name;
                input.referral.choicesMap.put(entry.getKey(), cvm);
            }
        }

        if (!strictReferenceData) {
            ensureReferralReferenceDataToServer(operation.baseUri, input.referral);
            ensureClientReferenceDataToServer(operation.baseUri, input.client);
        }

        // process funding source (after above code)
        if (input.referral.fundingSource != null) {
            if (hasText(input.referral.fundingSource)) {
                ResponseEntity<FundingSourceViewModel> response = getFundingSourceByName(operation.baseUri + apiPath,
                        input.referral.fundingSource);
                FundingSourceViewModel vm = response.getBody();
                input.referral.fundingSource = vm.getName();
            }
        }

        // check the client has a name to process
        if (!updateMode) {
            if ((hasText(input.client.firstName)) && (hasText(input.client.lastName))) {
                try {
                    input.referral.clientId = syncClientToServer(operation.baseUri, input.client);
                    input.referral.referralId = syncReferralToServer(operation.baseUri, input.referral);
                } catch (IllegalStateException e) {
                    log.error("failed to import at row: " + operation.row, e);
                }
            }
        } else {
            String referralCode = input.referral.referralCode;

            if (input.referral.referralCode == null && input.referral.importServiceName != null) {
                int sId = getServiceIdFromName(input.referral.importServiceName);
                var referral = this.referralsByService.get(sId).stream().filter(r -> r.clientFirstName.equals(input.client.firstName)
                                && r.clientLastName.equals(input.client.lastName)).findFirst();
                if (referral.isPresent()) {
                    var r = referral.get();
                    referralCode = referral.get().referralCode;
                    input.referral.referralCode = referralCode;
                    input.referral.clientId = r.clientId;
                }
            }

            if (referralCode != null) {
                // get the client id from the referral
                // since we refer to aggregates we probably don't have reference to the client to update it accurately
                String clientCode = getClientCodeByReferralCode(operation.baseUri, referralCode);
                input.client.code = clientCode;
                updateClientToServer(operation.baseUri, input.client);
                updateReferralToServer(operation.baseUri, input.referral);
            }
        }
    }

    private ListDefinitionEntryViewModel lookupOrCreateListDef(String listName, String lookup) {
        var listDefEntry = lookupExistingListDef(listName, lookup);
        if (listDefEntry.isPresent()) {
            return listDefEntry.get();
        } else {
            var ld = ListDefinitionEntryViewModel.builder()
                    .listName(listName)
                    .name(lookup)
                    .build();
            this.listDefEntries = listDefActor.createListDefinitionEntry(ld, false);
            return lookupExistingListDef(listName, lookup).get();
        }
    }

    @NotNull
    private Optional<ListDefinitionEntryViewModel> lookupExistingListDef(String listName, String lookup) {
        var listDefEntry = this.listDefEntries.get(listName);
        if (listDefEntry == null) {
            return Optional.empty();
        }
        return listDefEntry
                .stream()
                .filter(e -> e.name.equalsIgnoreCase(lookup))
                .findFirst();
    }

    private ResponseEntity<FundingSourceViewModel> getFundingSourceByName(String url, String name) {
        return getForEntity(url + "fundingSource/byName/{name}/", FundingSourceViewModel.class, name);
    }

    private void ensureReferralReferenceDataToServer(String uriBase, ReferralViewModel input) {
        if (hasText(input.fundingSource)) {
            FundingSourceViewModel vm = new FundingSourceViewModel();
            vm.name = input.fundingSource;
            Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "fundingSource/", vm);
        }
        if (hasText(input.localAuthority)) {
            LocalAuthorityViewModel vm = new LocalAuthorityViewModel();
            vm.name = input.localAuthority;
            Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "localAuthority/", vm);
        }
        if (hasText(input.exitReason)) {
            IdNameViewModel vm = new IdNameViewModel();
            vm.name = input.exitReason;
            Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "referrals/exitReason/", vm);
        }
        if (hasText(input.signpostedReason)) {
            IdNameViewModel vm = new IdNameViewModel();
            vm.name = input.signpostedReason;
            Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "referrals/signpostReason/", vm);
        }

    }

    private void ensureClientReferenceDataToServer(String uriBase, ClientViewModel input) {

        // TODO genders/disabilities/sexualOrient need to import by id, and use ImportProcessorTest sampleListDefImport.csv to create new ones
        // TODO otherwise we need to create a ClientInboundViewModel which has additional attributes such as 'gender'/'disability' string
        // TODO ethnicOrigin/firstLang/religion must exist otherwise we need to create additional attributes in ClientInboundViewModel
    }

    /** return id, or throw an exception */
    private long syncClientToServer(String uriBase, ClientViewModel input) {
        // TODO we could think about supplying a code if blank, as syncIndividualToServer below
        Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "clients/?enforceUniqueClient=true", input);
        return Long.parseLong(result.getId());
    }

    /** return id, or throw an exception */
    private long syncReferralToServer(String uriBase, ReferralViewModel input) {
        Result result = postAcceptingCreatedOrUnprocessableEntity(uriBase + apiPath + "referrals/", input);
        return Long.parseLong(result.getId());
    }

    private String getClientCodeByReferralCode(String uriBase, String referralCode) {
        ResponseEntity<ReferralViewModel> response = getForEntity(uriBase + apiPath + "/referrals/byCode/{code}/", ReferralViewModel.class, referralCode);
        String code = response.getBody().getClientCode();
        return code;
    }

    private void updateClientToServer(String uriBase, ClientViewModel input) {
        putEntity(uriBase + apiPath + "clients/byCode/" + input.code + "/", input);
    }

    private void updateReferralToServer(String uriBase, ReferralViewModel input) {
        putEntity(uriBase + apiPath + "referrals/byCode/" + input.referralCode + "/", input);
    }

}
