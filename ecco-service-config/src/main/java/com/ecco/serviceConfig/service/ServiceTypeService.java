package com.ecco.serviceConfig.service;

import com.ecco.serviceConfig.dom.ServiceType;
import com.ecco.serviceConfig.dom.ServiceTypeWorkflow;
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel;

import java.util.List;
import java.util.Optional;

public interface ServiceTypeService {

    List<Long> findAllIds();

    /**
     * @deprecated non-caching version. Use findOneDto
     */
    ServiceType findOne(Long id);

    ServiceTypeViewModel findOneDto(Integer id);

    ServiceTypeViewModel putOneDtoInCache(ServiceTypeViewModel serviceTypeViewModel);

    Optional<ServiceTypeWorkflow> findWorkflow(long serviceTypeId);

    void evictOneDtoInCache(Integer id);

}
