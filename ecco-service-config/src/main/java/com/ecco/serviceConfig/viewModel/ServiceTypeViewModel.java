package com.ecco.serviceConfig.viewModel;

import static java.util.stream.Collectors.joining;

import java.util.ArrayList;
import java.util.List;

import org.jspecify.annotations.NonNull;

import com.fasterxml.jackson.annotation.JsonIgnore;


public class ServiceTypeViewModel {

    /** Ignored when POSTing a new entity */
    public Integer id;

    public String name;

    public String processKey;

    public List<IdNameViewModel> supportOutcomes;

    public List<RiskAreaViewModel> riskAreas;

    public List<IdNameViewModel> questionGroups;

    /** Ordered list of names of tasks which are part of new referral wizard */
    public List<String> wizardTasks;

    /** Ordered list of all tasks */
    public List<TaskDefinitionEntryViewModel> taskDefinitionEntries;

    public Long primaryRelationshipId;

    /** Don't show when... */
    public boolean hideOnList;
    /** Don't show in the new referral wizard - only create via allocate/child referral process */
    public boolean hideOnNew;

    /**
     * @return the position (1-based) of the 'referralView' for this ServiceType, or 0 if it doesn't exist
     */
    @JsonIgnore
    public int getTaskCountToReferralView() {
        for (int i = 0; i < taskDefinitionEntries.size(); i++) {
            if (TaskDefinitionEntryViewModel.TASK_REFERALVIEW.equals(taskDefinitionEntries.get(i).name)) {
                return i + 1; // Cannot use orderby as it's not sequential
            }
        }
        return 0;
    }

    public boolean isWizardTask(String taskNameIn) {
        for (int i = 0; i < taskDefinitionEntries.size(); i++) {
            String taskName = taskDefinitionEntries.get(i).name;
            if (taskName.equalsIgnoreCase(taskNameIn)) {
                return true;
            }
            if (TaskDefinitionEntryViewModel.TASK_REFERALVIEW.equals(taskName)) {
                return false;
            }
        }
        return false;
    }

    public List<String> getTaskNamesToReferralView() {
        List<String> wizardTasks = new ArrayList<>();
        for (int i = 0; i < taskDefinitionEntries.size(); i++) {
            wizardTasks.add(taskDefinitionEntries.get(i).name);
            if (TaskDefinitionEntryViewModel.TASK_REFERALVIEW.equals(taskDefinitionEntries.get(i).name)) {
                return wizardTasks;
            }
        }
        return null;
    }

    public boolean hasTaskDefById(long taskDefId) {
        for (TaskDefinitionEntryViewModel entry: taskDefinitionEntries) {
            if (entry.taskDefId == taskDefId) {
                return true;
            }
        }
        return false;
    }

    @NonNull
    public TaskDefinitionEntryViewModel getTaskDefById(long taskDefId) {
        for (TaskDefinitionEntryViewModel entry: taskDefinitionEntries) {
            if (entry.taskDefId == taskDefId) {
                return entry;
            }
        }
        throw new IllegalArgumentException(taskDefId + " not found in tasks: " +
                taskDefinitionEntries.stream().map(tde -> String.valueOf(tde.taskDefId)).collect(joining(", ")));
    }

    @NonNull
    public TaskDefinitionEntryViewModel getTaskDefByNameStrict(@NonNull String name) {
        var task = getTaskDefByName(name);
        if (task == null) {
            throw new IllegalArgumentException(name + " not found in tasks: " +
                    taskDefinitionEntries.stream().map(tde -> tde.name).collect(joining(", ")));
        }
        return task;
    }

    // There are some commands which may not match a task, eg rotaVisit
    public TaskDefinitionEntryViewModel getTaskDefByName(@NonNull String name) {
        for (TaskDefinitionEntryViewModel entry: taskDefinitionEntries) {
            if (name.equals(entry.name)) {
                return entry;
            }
        }
        return null;
    }
}
