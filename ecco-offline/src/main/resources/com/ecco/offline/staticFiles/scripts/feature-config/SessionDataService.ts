import {SessionData, SessionDataGlobal} from "ecco-dto";
import {getFeatureConfigRepository} from "ecco-offline-data";

/**
 * A wrapper class to call SessionDataRepository
 */
export class SessionDataService {
    private static global: Promise<SessionDataGlobal> | null = null;
    private static features: Promise<SessionData> | null = null;

    /**
     * Get the session data (global only).
     */
    public static getGlobal(): Promise<SessionDataGlobal> {
        if (!SessionDataService.global) {
            SessionDataService.global = getFeatureConfigRepository().getSessionDataGlobal();
        }
        return SessionDataService.global;
    }

    /**
     * Get the session data (global and user).
     */
    public static getFeatures(): Promise<SessionData> {
        // TODO: Inline this: -> return Promise.resolve(getGlobalEccoAPI().sessionData);  // Note will continue using the one the page was loaded with
        if (!SessionDataService.features) {
            SessionDataService.features = getFeatureConfigRepository().getSessionData();
        }
        return SessionDataService.features;
        // .catch( () => new SessionData(null) ); // Really not sure why this was here but it results in broken stuff
    }
}
