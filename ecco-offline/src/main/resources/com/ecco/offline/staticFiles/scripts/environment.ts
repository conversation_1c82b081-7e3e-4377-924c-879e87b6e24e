import * as applicationProperties from "application-properties";
window.applicationProperties = applicationProperties;

import {isOfflinePage} from "ecco-dto";

const {applicationRootPath, resourceRootPath} = applicationProperties;

/** base for calls to online/offline pages - FIXME: We don't differentiate this way now */
export const baseURI = applicationRootPath + (isOfflinePage() ? "offline/" : "nav/");

/** Online only now. */
export const imagesBase = `${resourceRootPath}/themes/ecco/images/`;
