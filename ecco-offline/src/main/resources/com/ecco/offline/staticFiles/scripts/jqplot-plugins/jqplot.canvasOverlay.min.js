/**
 * jqPlot
 * Pure JavaScript plotting plugin using jQuery
 *
 * Version: 1.0.0b2_r1012
 *
 * Copyright (c) 2009-2011 <PERSON>
 * jqPlot is currently available for use in all personal or commercial projects 
 * under both the MIT (http://www.opensource.org/licenses/mit-license.php) and GPL 
 * version 2.0 (http://www.gnu.org/licenses/gpl-2.0.html) licenses. This means that you can 
 * choose the license that best suits your project and use it accordingly. 
 *
 * Although not required, the author would appreciate an email letting him 
 * know of any substantial use of jqPlot.  You can reach the author at: 
 * chris at jqplot dot com or see http://www.jqplot.com/info.php .
 *
 * If you are feeling kind and generous, consider supporting the project by
 * making a donation at: http://www.jqplot.com/donate.php .
 *
 * sprintf functions contained in jqplot.sprintf.js by <PERSON>:
 *
 *     version 2007.04.27
 *     author <PERSON>
 *     http://hexmen.com/blog/2007/03/printf-sprintf/
 *     http://hexmen.com/js/sprintf.js
 *     The author (<PERSON>) has placed this code in the public domain:
 *     "This code is unrestricted: you are free to use it however you like."
 *
 * included jsDate library by <PERSON>:
 *
 * Copyright (c) 2010-2011 <PERSON>
 *
 * jsDate is currently available for use in all personal or commercial projects 
 * under both the MIT and GPL version 2.0 licenses. This means that you can 
 * choose the license that best suits your project and use it accordingly.
 *
 * jsDate borrows many concepts and ideas from the Date Instance 
 * Methods by Ken Snyder along with some parts of Ken's actual code.
 * 
 * <PERSON>'s origianl Date Instance Methods and copyright notice:
 * 
 * Ken Snyder (ken d snyder at gmail dot com)
 * 2008-09-10
 * version 2.0.2 (http://kendsnyder.com/sandbox/date/)     
 * Creative Commons Attribution License 3.0 (http://creativecommons.org/licenses/by/3.0/)
 *
 * jqplotToImage function based on Larry Siden's export-jqplot-to-png.js.
 * Larry has generously given permission to adapt his code for inclusion
 * into jqPlot.
 *
 * Larry's original code can be found here:
 *
 * https://github.com/lsiden/export-jqplot-to-png
 * 
 * 
 */
(function(d){var f=0;d.jqplot.CanvasOverlay=function(o){var l=o||{};this.options={show:d.jqplot.config.enablePlugins,deferDraw:false};this.objects=[];this.objectNames=[];this.canvas=null;this.markerRenderer=new d.jqplot.MarkerRenderer({style:"line"});this.markerRenderer.init();this.highlightObjectIndex=null;if(l.objects){var q=l.objects,p;for(var m=0;m<q.length;m++){p=q[m];for(var r in p){switch(r){case"line":this.addLine(p[r]);break;case"horizontalLine":this.addHorizontalLine(p[r]);break;case"dashedHorizontalLine":this.addDashedHorizontalLine(p[r]);break;case"verticalLine":this.addVerticalLine(p[r]);break;case"dashedVerticalLine":this.addDashedVerticalLine(p[r]);break;default:break}}}}d.extend(true,this.options,l)};d.jqplot.CanvasOverlay.postPlotInit=function(o,n,m){var l=m||{};this.plugins.canvasOverlay=new d.jqplot.CanvasOverlay(l.canvasOverlay)};function h(){this.uid=null;this.type=null;this.gridStart=null;this.gridStop=null;this.tooltipWidthFactor=0;this.options={name:null,show:true,lineWidth:2,lineCap:"round",color:"#666666",shadow:true,shadowAngle:45,shadowOffset:1,shadowDepth:3,shadowAlpha:"0.07",xaxis:"xaxis",yaxis:"yaxis",showTooltip:false,showTooltipPrecision:0.6,tooltipLocation:"nw",fadeTooltip:true,tooltipFadeSpeed:"fast",tooltipOffset:4,tooltipFormatString:"%d, %d"}}function b(l){h.call(this);this.type="line";var m={start:[],stop:[]};d.extend(true,this.options,m,l);if(this.options.showTooltipPrecision<0.01){this.options.showTooltipPrecision=0.01}}b.prototype=new h();b.prototype.constructor=b;function e(l){h.call(this);this.type="horizontalLine";var m={y:null,xmin:null,xmax:null,xOffset:"6px",xminOffset:null,xmaxOffset:null};d.extend(true,this.options,m,l);if(this.options.showTooltipPrecision<0.01){this.options.showTooltipPrecision=0.01}}e.prototype=new h();e.prototype.constructor=e;function i(l){h.call(this);this.type="dashedHorizontalLine";var m={y:null,xmin:null,xmax:null,xOffset:"6px",xminOffset:null,xmaxOffset:null,dashPattern:[8,8]};d.extend(true,this.options,m,l);if(this.options.showTooltipPrecision<0.01){this.options.showTooltipPrecision=0.01}}i.prototype=new h();i.prototype.constructor=i;function c(l){h.call(this);this.type="verticalLine";var m={x:null,ymin:null,ymax:null,yOffset:"6px",yminOffset:null,ymaxOffset:null};d.extend(true,this.options,m,l);if(this.options.showTooltipPrecision<0.01){this.options.showTooltipPrecision=0.01}}c.prototype=new h();c.prototype.constructor=c;function k(l){h.call(this);this.type="dashedVerticalLine";this.start=null;this.stop=null;var m={x:null,ymin:null,ymax:null,yOffset:"6px",yminOffset:null,ymaxOffset:null,dashPattern:[8,8]};d.extend(true,this.options,m,l);if(this.options.showTooltipPrecision<0.01){this.options.showTooltipPrecision=0.01}}k.prototype=new h();k.prototype.constructor=k;d.jqplot.CanvasOverlay.prototype.addLine=function(m){var l=new b(m);l.uid=f++;this.objects.push(l);this.objectNames.push(l.options.name)};d.jqplot.CanvasOverlay.prototype.addHorizontalLine=function(m){var l=new e(m);l.uid=f++;this.objects.push(l);this.objectNames.push(l.options.name)};d.jqplot.CanvasOverlay.prototype.addDashedHorizontalLine=function(m){var l=new i(m);l.uid=f++;this.objects.push(l);this.objectNames.push(l.options.name)};d.jqplot.CanvasOverlay.prototype.addVerticalLine=function(m){var l=new c(m);l.uid=f++;this.objects.push(l);this.objectNames.push(l.options.name)};d.jqplot.CanvasOverlay.prototype.addDashedVerticalLine=function(m){var l=new k(m);l.uid=f++;this.objects.push(l);this.objectNames.push(l.options.name)};d.jqplot.CanvasOverlay.prototype.removeObject=function(l){if(d.type(l)=="number"){this.objects.splice(l,1);this.objectNames.splice(l,1)}else{var m=d.inArray(l,this.objectNames);if(m!=-1){this.objects.splice(m,1);this.objectNames.splice(m,1)}}};d.jqplot.CanvasOverlay.prototype.getObject=function(l){if(d.type(l)=="number"){return this.objects[l]}else{var m=d.inArray(l,this.objectNames);if(m!=-1){return this.objects[m]}}};d.jqplot.CanvasOverlay.prototype.get=d.jqplot.CanvasOverlay.prototype.getObject;d.jqplot.CanvasOverlay.prototype.clear=function(l){this.canvas._ctx.clearRect(0,0,this.canvas.getWidth(),this.canvas.getHeight())};d.jqplot.CanvasOverlay.prototype.draw=function(I){var w,t=this.objects,D=this.markerRenderer,q,E;if(this.options.show){this.canvas._ctx.clearRect(0,0,this.canvas.getWidth(),this.canvas.getHeight());for(var F=0;F<t.length;F++){w=t[F];var z=d.extend(true,{},w.options);if(w.options.show){D.shadow=w.options.shadow;w.tooltipWidthFactor=w.options.lineWidth/w.options.showTooltipPrecision;switch(w.type){case"line":D.style="line";z.closePath=false;q=[I.axes[w.options.xaxis].series_u2p(w.options.start[0]),I.axes[w.options.yaxis].series_u2p(w.options.start[1])];E=[I.axes[w.options.xaxis].series_u2p(w.options.stop[0]),I.axes[w.options.yaxis].series_u2p(w.options.stop[1])];w.gridStart=q;w.gridStop=E;D.draw(q,E,this.canvas._ctx,z);break;case"horizontalLine":if(w.options.y!=null){D.style="line";z.closePath=false;var n=I.axes[w.options.xaxis],Q,J,u=I.axes[w.options.yaxis].series_u2p(w.options.y),G=w.options.xminOffset||w.options.xOffset,r=w.options.xmaxOffset||w.options.xOffset;if(w.options.xmin!=null){Q=n.series_u2p(w.options.xmin)}else{if(G!=null){if(d.type(G)=="number"){Q=n.series_u2p(n.min+G)}else{if(d.type(G)=="string"){Q=n.series_u2p(n.min)+parseFloat(G)}}}}if(w.options.xmax!=null){J=n.series_u2p(w.options.xmax)}else{if(r!=null){if(d.type(r)=="number"){J=n.series_u2p(n.max-r)}else{if(d.type(r)=="string"){J=n.series_u2p(n.max)-parseFloat(r)}}}}if(J!=null&&Q!=null){w.gridStart=[Q,u];w.gridStop=[J,u];D.draw([Q,u],[J,u],this.canvas._ctx,z)}}break;case"dashedHorizontalLine":var m=w.options.dashPattern;var C=0;for(var K=0;K<m.length;K++){C+=m[K]}if(w.options.y!=null){D.style="line";z.closePath=false;var n=I.axes[w.options.xaxis],Q,J,u=I.axes[w.options.yaxis].series_u2p(w.options.y),G=w.options.xminOffset||w.options.xOffset,r=w.options.xmaxOffset||w.options.xOffset;if(w.options.xmin!=null){Q=n.series_u2p(w.options.xmin)}else{if(G!=null){if(d.type(G)=="number"){Q=n.series_u2p(n.min+G)}else{if(d.type(G)=="string"){Q=n.series_u2p(n.min)+parseFloat(G)}}}}if(w.options.xmax!=null){J=n.series_u2p(w.options.xmax)}else{if(r!=null){if(d.type(r)=="number"){J=n.series_u2p(n.max-r)}else{if(d.type(r)=="string"){J=n.series_u2p(n.max)-parseFloat(r)}}}}if(J!=null&&Q!=null){w.gridStart=[Q,u];w.gridStop=[J,u];var p=Math.ceil((J-Q)/C);var O=Q,M;for(var K=0;K<p;K++){for(var H=0;H<m.length;H+=2){M=O+m[H];D.draw([O,u],[M,u],this.canvas._ctx,z);O+=m[H];if(H<m.length-1){O+=m[H+1]}}}}}break;case"verticalLine":if(w.options.x!=null){D.style="line";z.closePath=false;var L=I.axes[w.options.yaxis],l,s,v=I.axes[w.options.xaxis].series_u2p(w.options.x),B=w.options.yminOffset||w.options.yOffset,o=w.options.ymaxOffset||w.options.yOffset;if(w.options.ymin!=null){l=L.series_u2p(w.options.ymin)}else{if(B!=null){if(d.type(B)=="number"){l=L.series_u2p(L.min-B)}else{if(d.type(B)=="string"){l=L.series_u2p(L.min)-parseFloat(B)}}}}if(w.options.ymax!=null){s=L.series_u2p(w.options.ymax)}else{if(o!=null){if(d.type(o)=="number"){s=L.series_u2p(L.max+o)}else{if(d.type(o)=="string"){s=L.series_u2p(L.max)+parseFloat(o)}}}}if(s!=null&&l!=null){w.gridStart=[v,l];w.gridStop=[v,s];D.draw([v,l],[v,s],this.canvas._ctx,z)}}break;case"dashedVerticalLine":var m=w.options.dashPattern;var C=0;for(var K=0;K<m.length;K++){C+=m[K]}if(w.options.x!=null){D.style="line";z.closePath=false;var L=I.axes[w.options.yaxis],l,s,v=I.axes[w.options.xaxis].series_u2p(w.options.x),B=w.options.yminOffset||w.options.yOffset,o=w.options.ymaxOffset||w.options.yOffset;if(w.options.ymin!=null){l=L.series_u2p(w.options.ymin)}else{if(B!=null){if(d.type(B)=="number"){l=L.series_u2p(L.min-B)}else{if(d.type(B)=="string"){l=L.series_u2p(L.min)-parseFloat(B)}}}}if(w.options.ymax!=null){s=L.series_u2p(w.options.ymax)}else{if(o!=null){if(d.type(o)=="number"){s=L.series_u2p(L.max+o)}else{if(d.type(o)=="string"){s=L.series_u2p(L.max)+parseFloat(o)}}}}if(s!=null&&l!=null){w.gridStart=[v,l];w.gridStop=[v,s];var p=Math.ceil((l-s)/C);var A=((p*C)-(l-s))/2;var O=l,M,N,P;for(var K=0;K<p;K++){for(var H=0;H<m.length;H+=2){M=O-m[H];if(M<s){M=s}if(O<s){O=s}D.draw([v,O],[v,M],this.canvas._ctx,z);O-=m[H];if(H<m.length-1){O-=m[H+1]}}}}}break;default:break}}}}};d.jqplot.CanvasOverlay.postPlotDraw=function(){var m=this.plugins.canvasOverlay;if(m&&m.highlightCanvas){m.highlightCanvas.resetCanvas();m.highlightCanvas=null}m.canvas=new d.jqplot.GenericCanvas();this.eventCanvas._elem.before(m.canvas.createElement(this._gridPadding,"jqplot-overlayCanvas-canvas",this._plotDimensions,this));m.canvas.setContext();if(!m.deferDraw){m.draw(this)}var l=document.createElement("div");m._tooltipElem=d(l);l=null;m._tooltipElem.addClass("jqplot-canvasOverlay-tooltip");m._tooltipElem.css({position:"absolute",display:"none"});this.eventCanvas._elem.before(m._tooltipElem);this.eventCanvas._elem.bind("mouseleave",{elem:m._tooltipElem},function(n){n.data.elem.hide()});var m=null};function j(q,o,p,n){var s=q.plugins.canvasOverlay;var m=s._tooltipElem;var l=o.options,t,r;m.html(d.jqplot.sprintf(l.tooltipFormatString,n[0],n[1]));switch(l.tooltipLocation){case"nw":t=p[0]+q._gridPadding.left-m.outerWidth(true)-l.tooltipOffset;r=p[1]+q._gridPadding.top-l.tooltipOffset-m.outerHeight(true);break;case"n":t=p[0]+q._gridPadding.left-m.outerWidth(true)/2;r=p[1]+q._gridPadding.top-l.tooltipOffset-m.outerHeight(true);break;case"ne":t=p[0]+q._gridPadding.left+l.tooltipOffset;r=p[1]+q._gridPadding.top-l.tooltipOffset-m.outerHeight(true);break;case"e":t=p[0]+q._gridPadding.left+l.tooltipOffset;r=p[1]+q._gridPadding.top-m.outerHeight(true)/2;break;case"se":t=p[0]+q._gridPadding.left+l.tooltipOffset;r=p[1]+q._gridPadding.top+l.tooltipOffset;break;case"s":t=p[0]+q._gridPadding.left-m.outerWidth(true)/2;r=p[1]+q._gridPadding.top+l.tooltipOffset;break;case"sw":t=p[0]+q._gridPadding.left-m.outerWidth(true)-l.tooltipOffset;r=p[1]+q._gridPadding.top+l.tooltipOffset;break;case"w":t=p[0]+q._gridPadding.left-m.outerWidth(true)-l.tooltipOffset;r=p[1]+q._gridPadding.top-m.outerHeight(true)/2;break;default:t=p[0]+q._gridPadding.left-m.outerWidth(true)-l.tooltipOffset;r=p[1]+q._gridPadding.top-l.tooltipOffset-m.outerHeight(true);break}m.css("left",t);m.css("top",r);if(l.fadeTooltip){m.stop(true,true).fadeIn(l.tooltipFadeSpeed)}else{m.show()}m=null}function g(y,p,r,o){var n=y[0];var m=y[1];var x=Math.round(r[0]);var w=Math.round(r[1]);var t=Math.round(p[0]);var s=Math.round(p[1]);var q=Math.sqrt(Math.pow(x-t,2)+Math.pow(w-s,2));var z=o*q;var v=Math.abs((t-x)*(m-w)-(s-w)*(n-x));var u=(v<z)?true:false;return u}function a(y,v,q,z,w){var x=w.plugins.canvasOverlay;var u=x.objects;var r=u.length;var t,m=false;var p;for(var s=0;s<r;s++){t=u[s];if(t.options.showTooltip){var o=g([v.x,v.y],t.gridStart,t.gridStop,t.tooltipWidthFactor);q=[w.axes[t.options.xaxis].series_p2u(v.x),w.axes[t.options.yaxis].series_p2u(v.y)];if(o&&x.highlightObjectIndex==null){switch(t.type){case"line":j(w,t,[v.x,v.y],q);break;case"horizontalLine":case"dashedHorizontalLine":j(w,t,[v.x,t.gridStart[1]],[q[0],t.options.y]);break;case"verticalLine":case"dashedVerticalLine":j(w,t,[t.gridStart[0],v.y],[t.options.x,q[1]]);break;default:break}x.highlightObjectIndex=s;m=true;break}else{if(o&&x.highlightObjectIndex!==s){p=x._tooltipElem;if(t.fadeTooltip){p.fadeOut(t.tooltipFadeSpeed)}else{p.hide()}switch(t.type){case"line":j(w,t,[v.x,v.y],q);break;case"horizontalLine":case"dashedHorizontalLine":j(w,t,[v.x,t.gridStart[1]],[q[0],t.options.y]);break;case"verticalLine":case"dashedVerticalLine":j(w,t,[t.gridStart[0],v.y],[t.options.x,q[1]]);break;default:break}x.highlightObjectIndex=s;m=true;break}else{if(o){switch(t.type){case"line":j(w,t,[v.x,v.y],q);break;case"horizontalLine":case"dashedHorizontalLine":j(w,t,[v.x,t.gridStart[1]],[q[0],t.options.y]);break;case"verticalLine":case"dashedVerticalLine":j(w,t,[t.gridStart[0],v.y],[t.options.x,q[1]]);break;default:break}m=true;break}}}}}if(!m&&x.highlightObjectIndex!==null){p=x._tooltipElem;t=x.getObject(x.highlightObjectIndex);if(t.fadeTooltip){p.fadeOut(t.tooltipFadeSpeed)}else{p.hide()}x.highlightObjectIndex=null}}d.jqplot.postInitHooks.push(d.jqplot.CanvasOverlay.postPlotInit);d.jqplot.postDrawHooks.push(d.jqplot.CanvasOverlay.postPlotDraw);d.jqplot.eventListenerHooks.push(["jqplotMouseMove",a])})(jQuery);