import {CommandQueue, CommandSource} from "ecco-commands";
import {
    apiClient,
    AsyncSessionData,
    CommandForm,
    CommandSubform,
    LoadingSpinner,
    withCommandForm
} from "ecco-components";
import {checkBox,
    possiblyModalForm,
    textInput,
    numberInput,
    SelectList,
    CreatableListOptions
} from "ecco-components-core";
import {ListDefinitionEntry, SessionData, SessionDataAjaxRepository} from "ecco-dto";
import {stringifyPossibleError, withAuthErrorHandler} from "ecco-offline-data";
import * as _ from "lodash";

import * as React from "react";
import {ClassAttributes, Fragment, ReactNode} from "react";
import {createInstance} from "react-async";
import {showInCommandForm} from "../components/CommandForm";
import {ListDefCommand} from "./commands";
import {Alert, Grid } from '@eccosolutions/ecco-mui';

// TODO ideally we don't want to reload the session just to get the latest list def details
const repository: SessionDataAjaxRepository = new SessionDataAjaxRepository(apiClient.withCachePeriod(0));

/**
 * LOAD DATA (copied from CalendarEntryForm)
 */
// The react-async 'createInstance' approach to loading data.
// This is very repetitive for each bit of data!
// NB Context is used as State too
interface ListDefinitionEntryContext {
    entry: ListDefinitionEntry;
}
// NB the data is loaded from the session - so we needed to clear server caches after every edit
// it doesn't have its own repository - even though ListDefinitionController exists for /listdefinitions
// but the feature-config-domain#getOrCreateEntry does build up the domain model hierarchy
// and an individual entry may require its parents to fully edit it, so we are best reloading the whole list
function loader(props: {entryId: number}): Promise<ListDefinitionEntryContext> {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return withAuthErrorHandler(
        repository.getListDefinitionsOrdered().then(listDefs => {
            return {entry: listDefs.filter(ld => ld.getId() == props.entryId).pop()};
        })
    );
}
const AsyncListDefinitionEntryData = createInstance<ListDefinitionEntryContext>(
    {promiseFn: loader},
    "AsyncListDefinitionEntryData"
);

// NB this is just concerned about loading the data for its children (not session data etc)
function withListDefinitionEntry(entryId: number, children: (value: ListDefinitionEntry) => ReactNode) {
    return <Fragment>
        <AsyncListDefinitionEntryData entryId={entryId}>
            <AsyncListDefinitionEntryData.Loading>
                <LoadingSpinner/>
            </AsyncListDefinitionEntryData.Loading>
            <AsyncListDefinitionEntryData.Resolved>
                {context => children(context.entry)}
            </AsyncListDefinitionEntryData.Resolved>
            <AsyncListDefinitionEntryData.Rejected>
                {error => {
                    console.error(error);
                    return stringifyPossibleError(error);
                }}
            </AsyncListDefinitionEntryData.Rejected>
        </AsyncListDefinitionEntryData>
    </Fragment>;
}
/**
 * LOAD FORM - non-jsx modal
 * This top level function instigates the commandform (in showInCommandForm), and data
 */
export function listDefinitionEntry(entryId?: number | undefined, fixedListName?: string | undefined) {
    showInCommandForm(
        <ListDefinitionEntryEditor entryId={entryId} fixedListName={fixedListName}/>
    );
}
/**
 * LOAD FORM - jsx
 * This top level function assumes the caller has loaded the required data - otherwise you'll get nothing.
 */
const ListDefinitionEntryEditor = (props: {entryId?: number | undefined, fixedListName?: string | undefined, formRef?: ((e: EditListDefForm) => void) | undefined}) =>
    withCommandForm(commandForm =>
        possiblyModalForm(
            "list definition entry",
            true, true,
            () => commandForm.cancelForm(),
            () => commandForm.submitForm(),
            false, // TODO could emitChangesTo and see if there are any commands
            false,
            props.entryId
                ? getEditListDefinitionEntrySubform(props.entryId, props.fixedListName, props.formRef, commandForm)
                : getNewListDefinitionEntrySubform(props.fixedListName || null, props.formRef, commandForm)
        )
    );

/**
 * This is concerned about rendering the form - with all the data is required
 * formRef must extend CommandSource.  Used to emitChangesTo(cmdQ) when submitting. Could also ask hasChanges()...
 */
function getNewListDefinitionEntrySubform(fixedListName: string, formRef: (c: EditListDefForm) => void,
                                    commandForm?: CommandForm | undefined) {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return <AsyncSessionData.Resolved>{sessionData =>
            <EditListDefForm
                ref={formRef}
                entry={null}
                fixedListName={fixedListName}
                readOnly={false}
                sessionData={sessionData}
                commandForm={commandForm}
            />
        }
        </AsyncSessionData.Resolved>
    ;
}
function getEditListDefinitionEntrySubform(entryId: number,
                                     fixedListName: string,
                                     formRef: (c: EditListDefForm) => void,
                                     commandForm?: CommandForm | undefined) {
    // ErrorBoundary is done in loader, showInCommandForm and possiblyModel
    return <AsyncSessionData.Resolved>{sessionData =>
            withListDefinitionEntry(entryId, entry =>
                <EditListDefForm
                    ref={formRef}
                    entry={entry}
                    readOnly={false}
                    sessionData={sessionData}
                    commandForm={commandForm}
                />
            )}
        </AsyncSessionData.Resolved>
    ;
}

interface Props extends ClassAttributes<EditListDefForm> {
    readOnly: boolean;
    entry: ListDefinitionEntry;
    fixedListName?: string | undefined;
    sessionData: SessionData;
}

interface State {
    businessKey: string;
    entryName: string;
    listName: string;
    disabled: boolean;
    default: boolean;
    order: number;
    parentEntryId: number;
    optionalValue: string;
    optionalDisplayName: string;
}

function isEmpty(str: any) {
    return !str || !str.length;
}

export class EditListDefForm extends CommandSubform<Props, State> implements CommandSource {

    public static showInModal(listDefId: number, fixedListName?: string | undefined) {
        listDefinitionEntry(listDefId, fixedListName);
    }

    constructor(props) {
        super(props);

        // TODO ideally we exclude the ability to have the same list as the parent
        // but this is too much for now since it depends dynamically on listNameSelect

        const e = this.props.entry;
        this.state = {
            entryName: e && e.getDto().name,
            listName: e ? e.getDto().listName : props.fixedListName,
            businessKey: e && e.getDto().businessKey,
            disabled: e && e.getDto().disabled,
            default: e && e.getDto().defaulted,
            order: e && e.getDto().order,
            parentEntryId: e && e.getDto().parentId,
            optionalValue: e && e.getDto().metadata && e.getDto().metadata.value,
            optionalDisplayName: e && e.getDto().metadata && e.getDto().metadata.displayName
        }
    }

    emitChangesTo(commandQueue: CommandQueue) {
        let cmd: ListDefCommand;

        if (this.props.entry) {
            cmd = new ListDefCommand("update", this.props.entry.getId());
        }
        else {
            cmd = new ListDefCommand("add", null);
            cmd.newName(this.state.entryName);
        }

        const e = this.props.entry;
        cmd.changeEntryName(e && e.getDto().name, this.state.entryName);
        cmd.changeListName(e && e.getDto().listName, this.state.listName);
        cmd.changeBusinessKey(e && e.getDto().businessKey, this.state.businessKey);
        cmd.changeParentId(e && e.getDto().parentId, this.state.parentEntryId);
        cmd.changeDisabled(e && e.getDto().disabled, this.state.disabled);
        cmd.changeDefault(e && e.getDto().defaulted, this.state.default);
        cmd.changeOrder(e && e.getDto().order, this.state.order);
        cmd.changeMetaValue(e && e.getDto().metadata && e.getDto().metadata.value, this.state.optionalValue);
        cmd.changeMetaDisplayName(e && e.getDto().metadata && e.getDto().metadata.displayName, this.state.optionalDisplayName);

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    getErrors(): string[] {
        type ListDefinitionEntryField = keyof State; //[P in keyof ListDefinitionEntryField]
        const requiredFields: ListDefinitionEntryField[] = ['entryName', 'listName'];
        return requiredFields.reduce( (errors, requiredField) => {
            if (isEmpty(this.state[requiredField])) {
                errors.push(`${requiredField} is required`);
            }
            return errors;
        }, []);
    }

    render() {
        const setter = state => this.setState(state);

        const uniqueListNames: string[] =_.uniqBy(this.props.sessionData.getListDefinitionEntriesAsFlatArray(), (le) => {
            return le.getListName();
        }).map((ld) => ld.getListName());

        const listNameOptions = uniqueListNames.map<CreatableListOptions>((listName) => {
            return {value: listName, label: listName};
        });
        const listNameInitial = listNameOptions.filter(o => o.value == this.state.listName).pop();
        const listNameChange = (o: {value: string, label: string}) => {
            setter({...this.state, listName: (o as CreatableListOptions).value})
        };
        return (
            <Grid container>
                {!this.props.fixedListName && <Grid item xs={12}>
                    <SelectList createNew={true}
                                options={listNameOptions}
                                value={listNameInitial}
                                onChange={(o) => listNameChange(o as CreatableListOptions)}/>
                </Grid>}
                <Grid item xs={12}>
                    {textInput("entryName", "entry name", setter, this.state, undefined, this.props.readOnly,
                        true)}
                </Grid>
                <Grid item xs={12}>
                    {textInput("businessKey", "business key", setter, this.state, undefined, this.props.readOnly,
                        true)}
                </Grid>
                <Grid item xs={2}>
                    {checkBox("disabled", "disabled", setter, this.state, this.props.readOnly)}
                </Grid>
                <Grid item xs={2}>
                    {checkBox("default", "default", setter, this.state, this.props.readOnly)}
                </Grid>
                <Grid item xs={2}>
                    {numberInput("order", "order", setter, this.state, this.props.readOnly)}
                </Grid>
                {/*<Grid item xs={2}>*/}
                {/*    {textInput("disabled", "parent entry (optional)", setter, this.state, this.props.readOnly)}*/}
                {/*</Grid>*/}
                <Grid item xs={12}>
                    {textInput("optionalValue", "other value (optional)", setter, this.state, undefined, this.props.readOnly)}
                </Grid>
                <Grid item xs={12}>
                    {textInput("optionalDisplayName", "other name (optional)", setter, this.state, undefined, this.props.readOnly)}
                </Grid>

                <Grid item xs={12}>
                    {(this.props.entry && this.props.entry.getDto().name != this.state.entryName) &&
                        <Alert severity="warning">Changing the 'entry name' will be reflected in all current and historical reference to this item. Consider if its best to disable this item and add a new one.</Alert>
                    }
                    {(this.props.entry && this.props.entry.getDto().businessKey != this.state.businessKey) &&
                        <Alert severity="warning">Changing the 'business key' will have an impact on any external linked systems</Alert>
                    }
                </Grid>

            </Grid>
        );
    }
}
export default EditListDefForm;
