import URI = require("URI");
import FeatureVote = domain.FeatureVote;
import * as cmdDtos from "ecco-dto/command-dto";
import * as commands from "ecco-commands";
import * as domain from "ecco-dto";
import {Mergeable} from "ecco-dto";

/** see FeatureVoteChangeCommandViewModel.java */
interface FeatureVoteChangeCommandDto extends cmdDtos.UpdateCommandDto {
    name: string;
    voteChange: cmdDtos.StringChangeOptional;
}

export class FeatureVoteChangeCommand extends commands.BaseUpdateCommand {
    constructor(private name: string, private fromVote: FeatureVote, private toVote: FeatureVote) {
        super(URI("feature-config/")
            .segmentCoded("")
            .toString());
    }

    public override canMerge(candidate: Mergeable) {
        // TODO check instanceof due to 'adapt' in CommandQueue.addCommand
        if (!(candidate instanceof FeatureVoteChangeCommand)) {
            return false;
        }

        var that = <FeatureVoteChangeCommand>candidate;

        // can merge if they cancel out
        return this.getCommandTargetUri() == that.getCommandTargetUri()
            && this.name == that.name
            && this.fromVote == that.toVote
            && this.toVote == that.fromVote;
    }

    public override merge(previousCommand: this): this {
        // If we can merge - then the result of a merge is that they cancel each other out
        return null;
    }

    public toDto(): FeatureVoteChangeCommandDto {
        return ({
                uuid: this.getUuid().toString(),
                commandUri: this.commandUri,
                timestamp: this.timestamp,
                name: this.name,
                voteChange: {from: FeatureVote[this.fromVote], to: FeatureVote[this.toVote]}
        });
    }
}


/** matches ListDefCommandViewModel.java */
export interface ListDefCommandDto extends cmdDtos.UpdateCommandDto {
    operation: string;
    id?: number;
    nameNew?: string; // for new (the dto says we shouldn't change this)
    entryName: cmdDtos.StringChangeOptional;
    listName: cmdDtos.StringChangeOptional;
    businessKey: cmdDtos.StringChangeOptional;
    parentId: cmdDtos.NumberChangeOptional;
    disabled: cmdDtos.BooleanChange;
    order: cmdDtos.NumberChangeOptional;
    defaultForList: cmdDtos.BooleanChange;
    metaValue: cmdDtos.StringChangeOptional;
    metaDisplayName: cmdDtos.StringChangeOptional;
}

export class ListDefCommand extends commands.BaseUpdateCommand {

    private nameNew: string;
    private entryNameChange: cmdDtos.StringChangeOptional;
    private listNameChange: cmdDtos.StringChangeOptional;
    private businessKeyChange: cmdDtos.StringChangeOptional;
    private orderChange: cmdDtos.NumberChangeOptional;
    private parentIdChange: cmdDtos.NumberChangeOptional;
    private disabledChange: cmdDtos.BooleanChange;
    private defaultChange: cmdDtos.BooleanChange;
    private metaValueChange: cmdDtos.StringChangeOptional;
    private metaDisplayNameChange: cmdDtos.StringChangeOptional;

    constructor(private operation: string, private entityId: number) {
        super(URI("feature-config/")
            .segmentCoded("listDef")
            .segmentCoded("")
            .toString());
    }

    public override canMerge(candidate: Mergeable) {
        return false;
    }

    public override merge(previousCommand: this): this {
        return null;
    }

    public newName(newName: string) {
        this.nameNew = newName;
        return this;
    }

    public changeEntryName(from: string, to: string) {
        this.entryNameChange = this.asStringChange(from, to);
        return this;
    }

    public changeListName(from: string, to: string) {
        this.listNameChange = this.asStringChange(from, to);
        return this;
    }

    public changeBusinessKey(from: string, to: string) {
        this.businessKeyChange = this.asStringChange(from, to);
        return this;
    }

    public changeParentId(from: number, to: number) {
        this.parentIdChange = this.asNumberChange(from, to);
        return this;
    }

    public changeDisabled(from: boolean, to: boolean) {
        this.disabledChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeDefault(from: boolean, to: boolean) {
        this.defaultChange = this.asBooleanChange(from, to);
        return this;
    }

    public changeOrder(from: number, to: number) {
        this.orderChange = this.asNumberChange(from, to);
        return this;
    }

    public changeMetaValue(from: string, to: string) {
        this.metaValueChange = this.asStringChange(from, to);
        return this;
    }

    public changeMetaDisplayName(from: string, to: string) {
        this.metaDisplayNameChange = this.asStringChange(from, to);
        return this;
    }

    /** We can build this command by throwing possibly 'non-change' data at it.  This will return 'true' only
     *  if the result was that there are changes to submit */
    public override hasChanges(): boolean {
        return this.entryNameChange != null
            || this.listNameChange != null
            || this.businessKeyChange != null
            || this.parentIdChange != null
            || this.disabledChange != null
            || this.defaultChange != null
            || this.orderChange != null
            || this.metaValueChange != null
            || this.metaDisplayNameChange != null
            || this.operation == "add";
    }

    public toDto(): ListDefCommandDto {
        return ({
                    operation: this.operation,
                    uuid: this.getUuid().toString(),
                    commandUri: this.commandUri,
                    timestamp: this.timestamp,
                    id: this.entityId,
                    nameNew: this.nameNew,
                    entryName: this.entryNameChange,
                    listName: this.listNameChange,
                    businessKey: this.businessKeyChange,
                    parentId: this.parentIdChange,
                    disabled: this.disabledChange,
                    defaultForList: this.defaultChange,
                    order: this.orderChange,
                    metaValue: this.metaValueChange,
                    metaDisplayName: this.metaDisplayNameChange
        });
    }
}

