package com.ecco.buildings.repositories;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.infrastructure.spring.data.CrudRepositoryWithFindOne;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.NullMarked;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

@NullMarked
public interface FixedContainerRepository extends QuerydslPredicateExecutor<FixedContainer>, CrudRepositoryWithFindOne<FixedContainer, Integer> {

    @Cacheable(value = CacheConfig.CACHE_BUILDINGS, key = "'byId-' + #buildingId")
    Optional<FixedContainer> findById(Integer buildingId);

    @Query("SELECT fc FROM FixedContainer fc LEFT JOIN FETCH fc.serviceRecipient WHERE fc.id = :buildingId")
    Optional<FixedContainer> findByIdWithServiceRecipient(@Param("buildingId") Integer buildingId);

    Optional<FixedContainer> findByExternalRef(String ref);

    @Query("SELECT fc FROM FixedContainer fc LEFT JOIN FETCH fc.serviceRecipient WHERE fc.externalRef = :ref")
    Optional<FixedContainer> findByExternalRefWithServiceRecipient(@Param("ref") String ref);

    Optional<FixedContainer> findByServiceRecipient_Id(int buildingSrId);

    @Query("SELECT fc FROM FixedContainer fc LEFT JOIN FETCH fc.serviceRecipient WHERE fc.serviceRecipient.id = :buildingSrId")
    Optional<FixedContainer> findByServiceRecipientIdWithServiceRecipient(@Param("buildingSrId") int buildingSrId);

    List<FixedContainer> findAllByIdIn(List<Integer> buildingId);

    @Query("SELECT fc FROM FixedContainer fc LEFT JOIN FETCH fc.serviceRecipient WHERE fc.id IN :buildingIds")
    List<FixedContainer> findAllByIdInWithServiceRecipient(@Param("buildingIds") List<Integer> buildingIds);

    List<FixedContainer> findAllByOrderByResourceTypeNameAsc();

    List<FixedContainer> findAllByResourceTypeId(Integer id);

    List<FixedContainer> findAllByParentId(int buildingId);

    @Query("SELECT fc FROM FixedContainer fc LEFT JOIN FETCH fc.serviceRecipient WHERE fc.parentId = :parentId")
    List<FixedContainer> findAllByParentIdWithServiceRecipient(@Param("parentId") int parentId);

    List<FixedContainer> findAllByParentIdInAndResourceTypeIdAndDisabledFalse(List<Integer> buildingId, Integer resourceTypeId);

    List<FixedContainer> findAllByParentIdAndResourceTypeId(int buildingId, Integer resourceTypeId);

    /** Find FixedContainers for a given resource type or child of that resource type.
     * e.g. for BUILDING_RESOURCETYPE_ID */
    @Query("FROM FixedContainer fc WHERE fc.resourceTypeId = :id OR fc.resourceType.parentId = :id")
    Stream<FixedContainer> findAllByResourceType_ParentId(@Param("id") Integer id);

    @Modifying
    @Query("UPDATE FixedContainer SET locationId = :to where id = :id")
    @CacheEvict(value = CacheConfig.CACHE_BUILDINGS, allEntries = true)
    void updateBuildingLocation(@Param("id") Integer id, @Param("to") Integer to);

    @NonNull
    @Override
    @CacheEvict(value = CacheConfig.CACHE_BUILDINGS, allEntries = true)
    <S extends FixedContainer> S save(@NonNull S entity);
}
