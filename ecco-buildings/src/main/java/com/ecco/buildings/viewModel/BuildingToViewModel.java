package com.ecco.buildings.viewModel;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.dom.AddressedLocation;
import org.mapstruct.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Converts FixedContainer entities to BuildingViewModel within transactional boundaries.
 * This converter has no dependencies on ecco-web-api, avoiding circular dependencies.
 * Uses MapStruct for efficient compile-time mapping generation.
 */
@Mapper(componentModel = "spring")
public abstract class BuildingToViewModel {

    @Mapping(source = "id", target = "buildingId")
    @Mapping(source = "serviceRecipient.serviceAllocationId", target = "serviceAllocationId")
    @Mapping(source = "resourceType.id", target = "resourceTypeId")
    @Mapping(source = "resourceType.name", target = "resourceTypeName")
    @Mapping(source = "parent.id", target = "parentId")
    @Mapping(source = "parent.name", target = "parentName")
    @Mapping(source = "chargeCategoryCombinations", target = "chargeCategoryCombinations", qualifiedByName = "mapChargeCombinations")
    @Mapping(source = "choicesMap", target = "choicesMap", qualifiedByName = "copyChoicesMap")
    @Mapping(source = "textMap", target = "textMap", qualifiedByName = "copyTextMap")
    public abstract BuildingViewModel toViewModel(FixedContainer input);

    @Named("mapChargeCombinations")
    protected java.util.List<BuildingViewModel.ChargeCategoryCombination> mapChargeCombinations(
            Map<String, List<FixedContainer.ChargeCategoryCombinations>> combinations) {
        if (combinations == null) return null;
        return combinations.get(FixedContainer.ChargeCategoryCombinationsKey).stream()
                .map(combo -> new BuildingViewModel.ChargeCategoryCombination(
                        combo.getChargeNameId(),
                        combo.getChargeCategoryId()))
                .toList();
    }

    @Named("copyTextMap")
    protected HashMap<String, String> copyTextMap(HashMap<String, String> textMap) {
        return textMap != null ? new HashMap<>(textMap) : new HashMap<>();
    }

    @Named("copyChoicesMap")
    protected HashMap<String, Integer> copyChoicesMap(HashMap<String, Integer> choicesMap) {
        return choicesMap != null ? new HashMap<>(choicesMap) : new HashMap<>();
    }

    @Named("convertLocation")
    protected BuildingViewModel.AddressedLocationViewModel convertLocation(AddressedLocation location) {
        if (location == null) return null;

        BuildingViewModel.AddressedLocationViewModel result = new BuildingViewModel.AddressedLocationViewModel();
        result.addressId = location.getId();
        result.buildingName = location.getBuildingName();
        result.buildingNumber = location.getBuildingNumber();
        result.line1 = location.getLine1();
        result.line2 = location.getLine2();
        result.line3 = location.getLine3();
        result.town = location.getTown();
        result.county = location.getCounty();
        result.postcode = location.getPostCode();
        result.country = location.getCountry();
        result.disabled = location.isDisabled();

        return result;
    }

}
