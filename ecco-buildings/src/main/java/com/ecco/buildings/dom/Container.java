package com.ecco.buildings.dom;

import java.util.UUID;

import javax.persistence.*;

import com.ecco.config.dom.ExternalSystem;
import com.ecco.config.dom.ListDefinitionEntry;
import com.ecco.dom.AddressedLocation;
import com.ecco.dom.CalendarableIntKeyedEntity;
import com.ecco.calendar.core.CalendarOwnerDefinition;
import com.ecco.calendar.core.CalendarOwnerDefinition.Builder;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

/*
 * SOME NOTES:
 * We have BookableResource which extends ContactImpl and uses the contacts table
 */

/**
 * A container is a physical 3D space that has can have a location which may be fixed, or have a parent container
 * where there is ultimately a fixed location at any point in time.
 */
@MappedSuperclass
@Getter
@Setter
public abstract class Container<T extends Container<T>> extends CalendarableIntKeyedEntity implements BookableResource {

    private static final long serialVersionUID = 1L;

    @Column
    private String name;

    /**
     * A resource is a non-human entity which can be used to satisfy a ResourceDemand.  The ResourceDemand will not be
     * for a specific instance of the ResourceType, but any compatible one.
     *
     * A resource type of "bed" may suffice, but each instance may also have additional characteristics which could be
     * considered when deciding the specific resource to allocate. These would be considered against any preferences
     * associated with the ResourceDemand.
     *
     * @see https://eccosolutions.atlassian.net/browse/ECCO-799 and related issues for some design background
     */
    @Column(name = "resourceTypeId")
    Integer resourceTypeId; // eg 'building' list, with children 'leased property' / 'own property' (see DEV-965-resourceType-listDef)

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "resourceTypeId", insertable = false, updatable = false)
    private ListDefinitionEntry resourceType; // NOTE: We've got an "is an resource (aka asset?)" class here.

    public static String RESOURCETYPE_LISTNAME = "resourceType";


    /** return the ultimate location of this item */
    public abstract AddressedLocation getTopParentLocation();

    @Override
    public String getDisplayName() {
        return getName();
    }

    private String externalRef; // external reference to another system

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "externalSource", insertable = false, updatable = false)
    @Fetch(FetchMode.SELECT)
    private ExternalSystem externalClientSource; // ... and which 'another system' that actually is.

    @SuppressWarnings("unchecked")
    public T withName(String name) {
        this.name = name;
        return (T) this;
    }

    @Override
    public Builder buildCalendarOwner(Builder builder) {
        String id = "bldg-" + UUID.randomUUID().toString();
        return builder
                .username("#" + id)
                .email(id + "@null.eccosolutions.co.uk")
                .lastName(name);
    }

    @Override
    public CalendarOwnerDefinition.Builder buildCalendarNamesUpdate(CalendarOwnerDefinition.Builder builder) {
        return builder
                .lastName(name);
    }

    @Override
    protected boolean isCalendarable() {
        return true;
    }
}
